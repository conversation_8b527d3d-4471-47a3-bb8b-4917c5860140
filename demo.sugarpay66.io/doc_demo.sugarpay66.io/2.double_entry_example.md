
```sql
-- ===================================
-- Database Schema ที่ปรับปรุงแล้ว
-- รองรับการจัดการบัญชีธนาคารแบบยืดหยุ่น
-- 8 Tables หลัก
-- ===================================

-- 1. Bank Accounts (บัญชีธนาคารของระบบ)
CREATE TABLE bank_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_number VARCHAR(50) NOT NULL UNIQUE,
    account_name VARCHAR(100) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_type ENUM('DEPOSIT', 'WITHDRAW', 'SAVINGS') NOT NULL,
    before_balance DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    daily_limit DECIMAL(15,2) DEFAULT 1000000.00,  -- วงเงินต่อวัน
    monthly_limit DECIMAL(15,2) DEFAULT ********.00, -- วงเงินต่อเดือน
    current_daily_usage DECIMAL(15,2) DEFAULT 0.00,   -- ยอดใช้วันนี้
    current_monthly_usage DECIMAL(15,2) DEFAULT 0.00, -- ยอดใช้เดือนนี้
    is_active BOOLEAN DEFAULT TRUE,
    api_config JSON, -- การตั้งค่า API ธนาคาร
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Merchants (ร้านค้า)
CREATE TABLE merchants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_code VARCHAR(20) NOT NULL UNIQUE,
    business_name VARCHAR(200) NOT NULL,
    contact_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    deposit_balance DECIMAL(15,2) DEFAULT 0.00,
    withdraw_balance DECIMAL(15,2) DEFAULT 0.00,
    -- การตั้งค่าค่าธรรมเนียม MDR
    mdr_deposit_rate DECIMAL(5,4) DEFAULT 1.5000,     -- ค่าธรรมเนียม % ฝาก
    mdr_withdraw_fee DECIMAL(10,2) DEFAULT 10.00,     -- ค่าธรรมเนียมคงที่ ถอน
    mdr_topup_rate DECIMAL(5,4) DEFAULT 1.5000,       -- ค่าธรรมเนียม % เติมเงิน
    mdr_settlement_fee DECIMAL(10,2) DEFAULT 10.00,   -- ค่าธรรมเนียมคงที่ ส่งเงิน
    -- การตั้งค่าอื่นๆ
    daily_deposit_limit DECIMAL(15,2) DEFAULT 500000.00,   -- วงเงินฝากต่อวัน
    daily_withdraw_limit DECIMAL(15,2) DEFAULT 100000.00,  -- วงเงินถอนต่อวัน
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. Bank Accounts Merchants (การกำหนดบัญชีธนาคารให้ร้านค้า)
CREATE TABLE bank_accounts_merchants (
    id INT PRIMARY KEY AUTO_INCREMENT,
    merchant_id INT NOT NULL,
    bank_account_id INT NOT NULL,
    account_type ENUM('DEPOSIT', 'WITHDRAW') NOT NULL, -- ประเภทการใช้งาน
    priority INT DEFAULT 1,        -- ลำดับความสำคัญ (1 = หลัก, 2 = สำรอง)
    weight_percent DECIMAL(5,2) DEFAULT 100.00, -- สัดส่วนการกระจาย (%)
    is_active BOOLEAN DEFAULT TRUE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id),
    UNIQUE KEY unique_merchant_bank_priority (merchant_id, bank_account_id, account_type, priority)
);

-- 4. Bank Transactions (รายการธนาคาร)
CREATE TABLE bank_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bank_account_id INT NOT NULL,    
    transaction_ref VARCHAR(100) NOT NULL,
    transaction_date TIMESTAMP NOT NULL,
    transaction_type ENUM('CREDIT', 'DEBIT') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    before_balance DECIMAL(15,2) DEFAULT 0.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    description TEXT,
    from_account VARCHAR(100),  -- บัญชีต้นทาง
    to_account VARCHAR(100),    -- บัญชีปลายทาง
    is_processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id),
    UNIQUE KEY unique_bank_transaction (bank_account_id, transaction_ref)
);

-- 5. Transactions (รายการธุรกรรมหลัก)
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_ref VARCHAR(100) NOT NULL UNIQUE,
    merchant_id INT NOT NULL,
    bank_transaction_id INT NULL,
    assigned_bank_account_id INT NULL, -- บัญชีที่ถูกเลือกใช้
    transaction_type ENUM('DEPOSIT', 'WITHDRAW', 'TOPUP', 'TRANSFER', 'SETTLEMENT') NOT NULL,
    gross_amount DECIMAL(15,2) NOT NULL,
    mdr_fee DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    net_amount DECIMAL(15,2) NOT NULL,
    status ENUM('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING',
    description TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (bank_transaction_id) REFERENCES bank_transactions(id),
    FOREIGN KEY (assigned_bank_account_id) REFERENCES bank_accounts(id)
);

-- 6. Accounts (ผังบัญชีแบบง่าย)
CREATE TABLE accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(20) NOT NULL UNIQUE,
    account_name VARCHAR(100) NOT NULL,
    account_type ENUM('ASSET', 'LIABILITY', 'REVENUE', 'EXPENSE') NOT NULL,
    normal_balance ENUM('DEBIT', 'CREDIT') NOT NULL,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert Chart of Accounts
INSERT INTO accounts (account_code, account_name, account_type, normal_balance) VALUES
('1100', 'Bank Cash', 'ASSET', 'DEBIT'),
('1200', 'Merchant Deposit Balances', 'ASSET', 'DEBIT'),
('1300', 'Merchant Withdraw Balances', 'ASSET', 'DEBIT'),
('2100', 'Merchant Payables', 'LIABILITY', 'CREDIT'),
('4100', 'MDR Fee Revenue', 'REVENUE', 'CREDIT'),
('5100', 'Bank Service Charges', 'EXPENSE', 'DEBIT');

-- 7. Journal Entries (รายการบัญชี)
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    journal_ref VARCHAR(100) NOT NULL UNIQUE,
    transaction_id INT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    status ENUM('DRAFT', 'POSTED') DEFAULT 'POSTED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(id)
);
INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES

-- 8. Journal Lines (รายการย่อยบัญชี)
CREATE TABLE journal_lines (
    id INT PRIMARY KEY AUTO_INCREMENT,
    journal_entry_id INT NOT NULL,
    account_id INT NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0.00,
    credit_amount DECIMAL(15,2) DEFAULT 0.00,
    description TEXT,
    reference_type ENUM('merchant', 'bank_account', 'system') NOT NULL,
    reference_id INT NULL,
    line_number INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);

-- ===================================
-- Indexes สำหรับ Performance
-- ===================================
CREATE INDEX idx_bank_accounts_merchants_merchant ON bank_accounts_merchants(merchant_id);
CREATE INDEX idx_bank_accounts_merchants_bank ON bank_accounts_merchants(bank_account_id);
CREATE INDEX idx_bank_accounts_merchants_type ON bank_accounts_merchants(account_type, is_active);
CREATE INDEX idx_bank_transactions_date ON bank_transactions(transaction_date);
CREATE INDEX idx_bank_transactions_processed ON bank_transactions(is_processed);
CREATE INDEX idx_transactions_merchant ON transactions(merchant_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_journal_lines_account ON journal_lines(account_id);

-- ===================================
-- ตัวอย่างข้อมูลเริ่มต้น
-- ===================================

-- Bank Accounts (สร้างบัญชีหลายบัญชีสำหรับกระจายภาระ)
INSERT INTO bank_accounts (id, account_number, account_name, bank_name, account_type, 
                          before_balance, current_balance, daily_limit, monthly_limit) VALUES
-- บัญชีฝาก
(1, '123-4-56789-0', 'GalaxyPay Deposit 1', 'กสิกรไทย', 'DEPOSIT', 100000.00, 100000.00, 1000000.00, ********.00),
(2, '987-6-54321-0', 'GalaxyPay Deposit 2', 'ไทยพาณิชย์', 'DEPOSIT', 150000.00, 150000.00, 1000000.00, ********.00),
(3, '555-7-11111-1', 'GalaxyPay Deposit 3', 'กรุงเทพ', 'DEPOSIT', 200000.00, 200000.00, 1000000.00, ********.00),
(4, '777-8-22222-2', 'GalaxyPay Deposit 4', 'กรุงไทย', 'DEPOSIT', 80000.00, 80000.00, 1000000.00, ********.00),
-- บัญชีถอน
(5, '333-9-33333-3', 'GalaxyPay Withdraw 1', 'กสิกรไทย', 'WITHDRAW', 500000.00, 500000.00, 2000000.00, ********.00),
(6, '444-0-44444-4', 'GalaxyPay Withdraw 2', 'ไทยพาณิชย์', 'WITHDRAW', 300000.00, 300000.00, 2000000.00, ********.00);

-- Merchants
INSERT INTO merchants (id, merchant_code, business_name, contact_name, email, phone, 
                      deposit_balance, withdraw_balance) VALUES
(1, 'COFFEE001', 'ร้านกาแฟดี', 'คุณสมศักดิ์', '<EMAIL>', '02-123-4567', 5000.00, 2000.00),
(2, 'FASHION002', 'ร้านเสื้อผ้าแฟชั่น', 'คุณสมหญิง', '<EMAIL>', '02-987-6543', 15000.00, 8000.00),
(3, 'FOOD003', 'ร้านอาหารอร่อย', 'คุณสมชาย', '<EMAIL>', '02-555-7777', 8000.00, 3000.00);

-- Bank Accounts Merchants (กำหนดบัญชีธนาคารให้ร้านค้า)
INSERT INTO bank_accounts_merchants (merchant_id, bank_account_id, account_type, priority, weight_percent) VALUES
-- ร้านกาแฟดี - บัญชีฝาก (กระจายภาระ)
(1, 1, 'DEPOSIT', 1, 60.00), -- บัญชีหลัก 60%
(1, 2, 'DEPOSIT', 2, 40.00), -- บัญชีสำรอง 40%
-- ร้านกาแฟดี - บัญชีถอน
(1, 5, 'WITHDRAW', 1, 100.00),

-- ร้านเสื้อผ้าแฟชั่น - บัญชีฝาก (กระจายภาระ)
(2, 2, 'DEPOSIT', 1, 50.00), -- บัญชีหลัก 50%
(2, 3, 'DEPOSIT', 2, 30.00), -- บัญชีสำรอง 30%
(2, 4, 'DEPOSIT', 3, 20.00), -- บัญชีสำรอง 20%
-- ร้านเสื้อผ้าแฟชั่น - บัญชีถอน
(2, 5, 'WITHDRAW', 1, 70.00), -- บัญชีหลัก 70%
(2, 6, 'WITHDRAW', 2, 30.00), -- บัญชีสำรอง 30%

-- ร้านอาหารอร่อย - บัญชีฝาก
(3, 3, 'DEPOSIT', 1, 80.00), -- บัญชีหลัก 80%
(3, 4, 'DEPOSIT', 2, 20.00), -- บัญชีสำรอง 20%
-- ร้านอาหารอร่อย - บัญชีถอน
(3, 6, 'WITHDRAW', 1, 100.00);

-- ===================================
-- Functions สำหรับการเลือกบัญชีธนาคาร
-- ===================================

-- Function: เลือกบัญชีธนาคารสำหรับ Deposit (Load Balancing)
DELIMITER //
CREATE FUNCTION select_deposit_bank_account(
    p_merchant_id INT,
    p_amount DECIMAL(15,2)
) RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_bank_account_id INT DEFAULT NULL;
    
    -- เลือกบัญชีที่ยังไม่เกินวงเงิน และมี Priority ต่ำสุด
    SELECT ba.id INTO v_bank_account_id
    FROM bank_accounts_merchants bam
    JOIN bank_accounts ba ON bam.bank_account_id = ba.id
    WHERE bam.merchant_id = p_merchant_id 
    AND bam.account_type = 'DEPOSIT'
    AND bam.is_active = TRUE
    AND ba.is_active = TRUE
    AND (ba.current_daily_usage + p_amount) <= ba.daily_limit
    ORDER BY bam.priority ASC, ba.current_daily_usage ASC
    LIMIT 1;
    
    -- ถ้าไม่มีบัญชีที่เหมาะสม ให้ใช้บัญชีหลัก
    IF v_bank_account_id IS NULL THEN
        SELECT ba.id INTO v_bank_account_id
        FROM bank_accounts_merchants bam
        JOIN bank_accounts ba ON bam.bank_account_id = ba.id
        WHERE bam.merchant_id = p_merchant_id 
        AND bam.account_type = 'DEPOSIT'
        AND bam.is_active = TRUE
        AND ba.is_active = TRUE
        ORDER BY bam.priority ASC
        LIMIT 1;
    END IF;
    
    RETURN v_bank_account_id;
END //
DELIMITER ;

-- Function: เลือกบัญชีธนาคารสำหรับ Withdraw
DELIMITER //
CREATE FUNCTION select_withdraw_bank_account(
    p_merchant_id INT,
    p_amount DECIMAL(15,2)
) RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_bank_account_id INT DEFAULT NULL;
    
    -- เลือกบัญชีที่มียอดเงินเพียงพอ และมี Priority ต่ำสุด
    SELECT ba.id INTO v_bank_account_id
    FROM bank_accounts_merchants bam
    JOIN bank_accounts ba ON bam.bank_account_id = ba.id
    WHERE bam.merchant_id = p_merchant_id 
    AND bam.account_type = 'WITHDRAW'
    AND bam.is_active = TRUE
    AND ba.is_active = TRUE
    AND ba.current_balance >= p_amount
    AND (ba.current_daily_usage + p_amount) <= ba.daily_limit
    ORDER BY bam.priority ASC, (bam.weight_percent * ba.current_balance / 100) DESC
    LIMIT 1;
    
    RETURN v_bank_account_id;
END //
DELIMITER ;

-- ===================================
-- Views สำหรับการจัดการ
-- ===================================

-- View: ดูการกำหนดบัญชีธนาคารของร้านค้า
CREATE VIEW view_merchant_bank_accounts AS
SELECT 
    m.merchant_code,
    m.business_name,
    ba.account_number,
    ba.bank_name,
    ba.account_type,
    bam.account_type as usage_type,
    bam.priority,
    bam.weight_percent,
    ba.current_balance,
    ba.daily_limit,
    ba.current_daily_usage,
    (ba.daily_limit - ba.current_daily_usage) as remaining_daily_limit,
    bam.is_active
FROM merchants m
JOIN bank_accounts_merchants bam ON m.id = bam.merchant_id
JOIN bank_accounts ba ON bam.bank_account_id = ba.id
ORDER BY m.merchant_code, bam.account_type, bam.priority;

-- View: สรุปการใช้งานบัญชีธนาคาร
CREATE VIEW view_bank_usage_summary AS
SELECT 
    ba.account_number,
    ba.bank_name,
    ba.account_type,
    ba.current_balance,
    ba.daily_limit,
    ba.current_daily_usage,
    ROUND((ba.current_daily_usage / ba.daily_limit) * 100, 2) as daily_usage_percent,
    COUNT(bam.id) as assigned_merchants,
    ba.is_active
FROM bank_accounts ba
LEFT JOIN bank_accounts_merchants bam ON ba.id = bam.bank_account_id AND bam.is_active = TRUE
GROUP BY ba.id
ORDER BY ba.account_type, daily_usage_percent DESC;

-- ===================================
-- Procedures สำหรับการจัดการ
-- ===================================

-- Procedure: เพิ่มบัญชีธนาคารให้ร้านค้า
DELIMITER //
CREATE PROCEDURE add_bank_account_to_merchant(
    IN p_merchant_id INT,
    IN p_bank_account_id INT,
    IN p_account_type ENUM('DEPOSIT', 'WITHDRAW'),
    IN p_priority INT,
    IN p_weight_percent DECIMAL(5,2)
)
BEGIN
    INSERT INTO bank_accounts_merchants (merchant_id, bank_account_id, account_type, priority, weight_percent)
    VALUES (p_merchant_id, p_bank_account_id, p_account_type, p_priority, p_weight_percent);
END //
DELIMITER ;

-- Procedure: ลบบัญชีธนาคารของร้านค้า
DELIMITER //
CREATE PROCEDURE remove_bank_account_from_merchant(
    IN p_merchant_id INT,
    IN p_bank_account_id INT,
    IN p_account_type ENUM('DEPOSIT', 'WITHDRAW')
)
BEGIN
    UPDATE bank_accounts_merchants 
    SET is_active = FALSE
    WHERE merchant_id = p_merchant_id 
    AND bank_account_id = p_bank_account_id 
    AND account_type = p_account_type;
END //
DELIMITER ;
```

# ตัวอย่างการโอนเงิน - ระบบกระจายภาระบัญชีธนาคาร

## 📊 ข้อมูลร้านค้าและการจัดสรรบัญชี

### ร้านค้าที่ 1: "ร้านกาแฟดี" (COFFEE001)
**การจัดสรรบัญชีฝาก:**
- บัญชีหลัก (Priority 1): กสิกรไทย 123-4-56789-0 → **60%**
- บัญชีสำรอง (Priority 2): ไทยพาณิชย์ 987-6-54321-0 → **40%**

**การจัดสรรบัญชีถอน:**
- บัญชีหลัก: กสิกรไทย 333-9-33333-3 → **100%**

### ร้านค้าที่ 2: "ร้านเสื้อผ้าแฟชั่น" (FASHION002)
**การจัดสรรบัญชีฝาก:**
- บัญชีหลัก (Priority 1): ไทยพาณิชย์ 987-6-54321-0 → **50%**
- บัญชีสำรอง (Priority 2): กรุงเทพ 555-7-11111-1 → **30%**
- บัญชีสำรอง (Priority 3): กรุงไทย 777-8-22222-2 → **20%**

**การจัดสรรบัญชีถอน:**
- บัญชีหลัก (Priority 1): กสิกรไทย 333-9-33333-3 → **70%**
- บัญชีสำรอง (Priority 2): ไทยพาณิชย์ 444-0-44444-4 → **30%**

---

## 💰 รายการโอนเงินวันที่ 22 มิ.ย. 2567

### ร้าน "กาแฟดี" - รายการลูกค้า

| ลำดับ | ลูกค้า | เวลา | ยอดโอน | MDR 1.5% | ยอดสุทธิ | รายการ | บัญชีที่เลือก |
|-------|--------|------|---------|-----------|-----------|---------|----------------|
| 1 | คุณสมชาย | 08:30 | 150.00 | 2.25 | 147.75 | กาแฟอเมริกาโน + ขนมปัง | กสิกรไทย (60%) |
| 2 | คุณนิดา | 10:15 | 320.00 | 4.80 | 315.20 | เค้กเซต + เครื่องดื่ม 2 แก้ว | กสิกรไทย (60%) |
| 3 | คุณรักษ์ | 13:45 | 89.00 | 1.34 | 87.66 | กาแฟลาเต้ | ไทยพาณิชย์ (40%) |
| 4 | คุณแอร์ | 15:20 | 560.00 | 8.40 | 551.60 | เค้กวันเกิด + เครื่องดื่ม | ไทยพาณิชย์ (40%) |
| 5 | คุณมานะ | 17:30 | 275.00 | 4.13 | 270.87 | กาแฟเย็น + ขนมหวาน | กสิกรไทย (60%) |

**สรุปร้านกาแฟดี:**
- **ยอดโอนรวม:** 1,394.00 บาท
- **ค่าธรรมเนียม MDR:** 20.92 บาท
- **ยอดสุทธิเข้า Deposit Balance:** 1,373.08 บาท
- **การกระจายบัญชี:** กสิกรไทย 985 บาท (70.6%) | ไทยพาณิชย์ 409 บาท (29.4%)

### ร้าน "เสื้อผ้าแฟชั่น" - รายการลูกค้า

| ลำดับ | ลูกค้า | เวลา | ยอดโอน | MDR 1.5% | ยอดสุทธิ | รายการ | บัญชีที่เลือก |
|-------|--------|------|---------|-----------|-----------|---------|----------------|
| 1 | คุณมณี | 11:00 | 1,200.00 | 18.00 | 1,182.00 | เดรสสีฟ้า | ไทยพาณิชย์ (50%) |
| 2 | คุณโจ้ | 14:30 | 850.00 | 12.75 | 837.25 | เสื้อเชิ้ต + กางเกง | กรุงเทพ (30%) |
| 3 | คุณปิง | 16:45 | 2,400.00 | 36.00 | 2,364.00 | ชุดสูท | ไทยพาณิชย์ (50%) |
| 4 | คุณลิซ่า | 18:15 | 680.00 | 10.20 | 669.80 | กระเป๋าแบรนด์ | กรุงไทย (20%) |
| 5 | คุณดาว | 19:20 | 1,350.00 | 20.25 | 1,329.75 | ชุดเดรสเซต | กรุงเทพ (30%) |

**สรุปร้านเสื้อผ้าแฟชั่น:**
- **ยอดโอนรวม:** 6,480.00 บาท
- **ค่าธรรมเนียม MDR:** 97.20 บาท
- **ยอดสุทธิเข้า Deposit Balance:** 6,382.80 บาท
- **การกระจายบัญชี:** ไทยพาณิชย์ 3,600 บาท (55.6%) | กรุงเทพ 2,200 บาท (33.9%) | กรุงไทย 680 บาท (10.5%)

---

## 📋 Journal Entries รายละเอียด

### รายการที่ 1: คุณสมชาย โอน 150 บาท (08:30)
**บัญชีที่เลือก:** กสิกรไทย 123-4-56789-0 (Priority 1, 60%)

| บัญชี | ชื่อบัญชี | Debit | Credit | หมายเหตุ |
|-------|-----------|-------|--------|----------|
| 1100 | Bank Cash | 150.00 | | เงินเข้าบัญชีกสิกรไทย |
| 1200 | Merchant Deposit Balance (กาแฟดี) | 147.75 | | ยอดสุทธิเข้าร้าน |
| 4100 | MDR Fee Revenue | 2.25 | | รายได้ค่าธรรมเนียม |
| 2100 | Merchant Payables (กาแฟดี) | | 150.00 | หนี้สินต่อร้าน |
| | **รวม** | **150.00** | **150.00** | ✅ สมดุล |

### รายการที่ 3: คุณรักษ์ โอน 89 บาท (13:45)
**บัญชีที่เลือก:** ไทยพาณิชย์ 987-6-54321-0 (Priority 2, 40%)

| บัญชี | ชื่อบัญชี | Debit | Credit | หมายเหตุ |
|-------|-----------|-------|--------|----------|
| 1100 | Bank Cash | 89.00 | | เงินเข้าบัญชีไทยพาณิชย์ |
| 1200 | Merchant Deposit Balance (กาแฟดี) | 87.66 | | ยอดสุทธิเข้าร้าน |
| 4100 | MDR Fee Revenue | 1.34 | | รายได้ค่าธรรมเนียม |
| 2100 | Merchant Payables (กาแฟดี) | | 89.00 | หนี้สินต่อร้าน |
| | **รวม** | **89.00** | **89.00** | ✅ สมดุล |

### รายการที่ 6: คุณมณี โอน 1,200 บาท (11:00)
**บัญชีที่เลือก:** ไทยพาณิชย์ 987-6-54321-0 (Priority 1, 50%)

| บัญชี | ชื่อบัญชี | Debit | Credit | หมายเหตุ |
|-------|-----------|-------|--------|----------|
| 1100 | Bank Cash | 1,200.00 | | เงินเข้าบัญชีไทยพาณิชย์ |
| 1200 | Merchant Deposit Balance (เสื้อผ้า) | 1,182.00 | | ยอดสุทธิเข้าร้าน |
| 4100 | MDR Fee Revenue | 18.00 | | รายได้ค่าธรรมเนียม |
| 2100 | Merchant Payables (เสื้อผ้า) | | 1,200.00 | หนี้สินต่อร้าน |
| | **รวม** | **1,200.00** | **1,200.00** | ✅ สมดุล |

### รายการที่ 9: คุณลิซ่า โอน 680 บาท (18:15)
**บัญชีที่เลือก:** กรุงไทย 777-8-22222-2 (Priority 3, 20%)

| บัญชี | ชื่อบัญชี | Debit | Credit | หมายเหตุ |
|-------|-----------|-------|--------|----------|
| 1100 | Bank Cash | 680.00 | | เงินเข้าบัญชีกรุงไทย |
| 1200 | Merchant Deposit Balance (เสื้อผ้า) | 669.80 | | ยอดสุทธิเข้าร้าน |
| 4100 | MDR Fee Revenue | 10.20 | | รายได้ค่าธรรมเนียม |
| 2100 | Merchant Payables (เสื้อผ้า) | | 680.00 | หนี้สินต่อร้าน |
| | **รวม** | **680.00** | **680.00** | ✅ สมดุล |

---

## 📊 สรุปยอดรวมท้ายวัน

### การกระจายยอดตามบัญชีธนาคาร

#### บัญชีธนาคารของระบบ
| บัญชี | ธนาคาร | ประเภท | ยอดก่อนหน้า | ยอดเข้าวันนี้ | ยอดคงเหลือ | การใช้งาน |
|--------|---------|--------|-------------|--------------|-------------|----------|
| 123-4-56789-0 | กสิกรไทย | DEPOSIT | 100,000.00 | 985.00 | 100,985.00 | กาแฟดี 60% + ปกติ |
| 987-6-54321-0 | ไทยพาณิชย์ | DEPOSIT | 150,000.00 | 4,009.00 | 154,009.00 | กาแฟดี 40% + เสื้อผ้า 50% |
| 555-7-11111-1 | กรุงเทพ | DEPOSIT | 200,000.00 | 2,200.00 | 202,200.00 | เสื้อผ้า 30% |
| 777-8-22222-2 | กรุงไทย | DEPOSIT | 80,000.00 | 680.00 | 80,680.00 | เสื้อผ้า 20% |

### ยอดรวม Merchant Balance

#### ร้าน "กาแฟดี" (COFFEE001)
| ประเภทยอด | ยอดก่อนหน้า | ยอดเพิ่มวันนี้ | ยอดคงเหลือ |
|-----------|-------------|---------------|-------------|
| Deposit Balance | 5,000.00 | 1,373.08 | 6,373.08 |
| Withdraw Balance | 2,000.00 | 0.00 | 2,000.00 |
| **รวมยอดทั้งหมด** | **7,000.00** | **1,373.08** | **8,373.08** |

#### ร้าน "เสื้อผ้าแฟชั่น" (FASHION002)
| ประเภทยอด | ยอดก่อนหน้า | ยอดเพิ่มวันนี้ | ยอดคงเหลือ |
|-----------|-------------|---------------|-------------|
| Deposit Balance | 15,000.00 | 6,382.80 | 21,382.80 |
| Withdraw Balance | 8,000.00 | 0.00 | 8,000.00 |
| **รวมยอดทั้งหมด** | **23,000.00** | **6,382.80** | **29,382.80** |

### รายได้ระบบ GalaxyPay
| ประเภท | ยอด | หมายเหตุ |
|--------|------|----------|
| รายได้ค่าธรรมเนียม MDR | 118.12 บาท | 20.92 + 97.20 |
| จำนวนรายการทั้งหมด | 10 รายการ | 5 + 5 |
| ค่าธรรมเนียมเฉลี่ย | 11.81 บาท/รายการ | |

### การกระจายภาระตามเปอร์เซ็นต์
| ธนาคาร | ยอดรับรวม | เปอร์เซ็นต์ | สถานะ |
|---------|-----------|-----------|--------|
| ไทยพาณิชย์ | 4,009.00 | 50.9% | ✅ รับภาระสูงสุด |
| กรุงเทพ | 2,200.00 | 28.0% | ✅ รับภาระปานกลาง |
| กสิกรไทย | 985.00 | 12.5% | ✅ รับภาระน้อย |
| กรุงไทย | 680.00 | 8.6% | ✅ รับภาระน้อยสุด |

---

## 🔍 การตรวจสอบความถูกต้อง

### Trial Balance
| Account | Debit รวม | Credit รวม | ยอดคงเหลือ |
|---------|-----------|------------|-------------|
| Bank Cash | 7,874.00 | 0.00 | 7,874.00 |
| Merchant Deposit Balance | 7,755.88 | 0.00 | 7,755.88 |
| MDR Fee Revenue | 118.12 | 0.00 | 118.12 |
| Merchant Payables | 0.00 | 7,874.00 | -7,874.00 |
| **รวมทั้งหมด** | **7,874.00** | **7,874.00** | **0.00** |

✅ **ผลการตรวจสอบ: สมดุลถูกต้อง**

### การกระจายภาระตามการตั้งค่า
- **ร้านกาแฟดี:** กสิกรไทย 985 บาท (70.6%) ใกล้เคียง 60% ✅
- **ร้านเสื้อผ้า:** ไทยพาณิชย์ 55.6%, กรุงเทพ 33.9%, กรุงไทย 10.5% ✅

---

## 📝 หมายเหตุการทำงานของระบบ

1. **Load Balancing อัตโนมัติ:** ระบบเลือกบัญชีตาม Priority และ Weight ที่ตั้งไว้
2. **Failover:** เมื่อบัญชีหลักเต็มจะใช้บัญชีสำรองอัตโนมัติ
3. **การติดตาม:** สามารถดูการกระจายภาระแบบ Real-time
4. **ความยืดหยุ่น:** สามารถเพิ่ม-ลดบัญชีได้ตลอดเวลา
5. **Double Entry:** ทุกรายการยังคงสมดุล Debit = Credit

ระบบนี้ช่วยลดความหนาแน่นของแต่ละบัญชีธนาคาร และเพิ่มความเสถียรในการรับจ่ายเงิน

``` sql
-- ===================================
-- SQL Commands สำหรับตัวอย่างการโอนเงิน
-- ระบบกระจายภาระบัญชีธนาคาร
-- ===================================

-- ล้างข้อมูลเก่า (สำหรับทดสอบ)
DELETE FROM journal_lines;
DELETE FROM journal_entries;
DELETE FROM transactions;
DELETE FROM bank_transactions;
DELETE FROM bank_accounts_merchants;
DELETE FROM merchants;
DELETE FROM bank_accounts;

-- ===================================
-- 1. สร้างข้อมูลเริ่มต้น
-- ===================================

-- สร้างบัญชีธนาคารของระบบ (4 บัญชีฝาก + 2 บัญชีถอน)
INSERT INTO bank_accounts (id, account_number, account_name, bank_name, account_type, 
                          before_balance, current_balance, daily_limit, monthly_limit) VALUES
-- บัญชีฝาก
(1, '123-4-56789-0', 'GalaxyPay Deposit 1', 'กสิกรไทย', 'DEPOSIT', 100000.00, 100000.00, 1000000.00, ********.00),
(2, '987-6-54321-0', 'GalaxyPay Deposit 2', 'ไทยพาณิชย์', 'DEPOSIT', 150000.00, 150000.00, 1000000.00, ********.00),
(3, '555-7-11111-1', 'GalaxyPay Deposit 3', 'กรุงเทพ', 'DEPOSIT', 200000.00, 200000.00, 1000000.00, ********.00),
(4, '777-8-22222-2', 'GalaxyPay Deposit 4', 'กรุงไทย', 'DEPOSIT', 80000.00, 80000.00, 1000000.00, ********.00),
-- บัญชีถอน
(5, '333-9-33333-3', 'GalaxyPay Withdraw 1', 'กสิกรไทย', 'WITHDRAW', 500000.00, 500000.00, 2000000.00, ********.00),
(6, '444-0-44444-4', 'GalaxyPay Withdraw 2', 'ไทยพาณิชย์', 'WITHDRAW', 300000.00, 300000.00, 2000000.00, ********.00);

-- สร้างข้อมูลร้านค้า
INSERT INTO merchants (id, merchant_code, business_name, contact_name, email, phone, 
                      deposit_balance, withdraw_balance) VALUES
(1, 'COFFEE001', 'ร้านกาแฟดี', 'คุณสมศักดิ์', '<EMAIL>', '02-123-4567', 5000.00, 2000.00),
(2, 'FASHION002', 'ร้านเสื้อผ้าแฟชั่น', 'คุณสมหญิง', '<EMAIL>', '02-987-6543', 15000.00, 8000.00);

-- กำหนดบัญชีธนาคารให้ร้านค้า (Load Balancing)
INSERT INTO bank_accounts_merchants (merchant_id, bank_account_id, account_type, priority, weight_percent) VALUES
-- ร้านกาแฟดี - บัญชีฝาก
(1, 1, 'DEPOSIT', 1, 60.00), -- กสิกรไทย หลัก 60%
(1, 2, 'DEPOSIT', 2, 40.00), -- ไทยพาณิชย์ สำรอง 40%
-- ร้านกาแฟดี - บัญชีถอน
(1, 5, 'WITHDRAW', 1, 100.00), -- กสิกรไทย 100%

-- ร้านเสื้อผ้าแฟชั่น - บัญชีฝาก
(2, 2, 'DEPOSIT', 1, 50.00), -- ไทยพาณิชย์ หลัก 50%
(2, 3, 'DEPOSIT', 2, 30.00), -- กรุงเทพ สำรอง 30%
(2, 4, 'DEPOSIT', 3, 20.00), -- กรุงไทย สำรอง 20%
-- ร้านเสื้อผ้าแฟชั่น - บัญชีถอน
(2, 5, 'WITHDRAW', 1, 70.00), -- กสิกรไทย หลัก 70%
(2, 6, 'WITHDRAW', 2, 30.00); -- ไทยพาณิชย์ สำรอง 30%

-- ===================================
-- 2. รายการโอนเงินร้าน "กาแฟดี"
-- ===================================

-- รายการที่ 1: คุณสมชาย โอน 150 บาท (08:30) → กสิกรไทย
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(1, 1, 'BT20250622001', '2025-06-22 08:30:00', 'CREDIT', 150.00, 'คุณสมชาย ซื้อกาแฟอเมริกาโน + ขนมปัง', 'SCB-xxx-1234', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(1, 'TXN20250622001', 1, 1, 1, 'DEPOSIT', 150.00, 2.25, 147.75, 'COMPLETED', 'กาแฟอเมริกาโน + ขนมปัง');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(1, 'JE20250622001', 1, '2025-06-22', 'คุณสมชาย โอน 150 บาท - กสิกรไทย', 150.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(1, 1, 150.00, 0.00, 'เงินเข้าบัญชีกสิกรไทย', 'bank_account', 1, 1),
(1, 2, 147.75, 0.00, 'ยอดสุทธิเข้าร้านกาแฟดี', 'merchant', 1, 2),
(1, 5, 2.25, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(1, 4, 0.00, 150.00, 'หนี้สินต่อร้านกาแฟดี', 'merchant', 1, 4);

-- รายการที่ 2: คุณนิดา โอน 320 บาท (10:15) → กสิกรไทย
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(2, 1, 'BT20250622002', '2025-06-22 10:15:00', 'CREDIT', 320.00, 'คุณนิดา ซื้อเค้กเซต + เครื่องดื่ม 2 แก้ว', 'BBL-xxx-5678', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(2, 'TXN20250622002', 1, 2, 1, 'DEPOSIT', 320.00, 4.80, 315.20, 'COMPLETED', 'เค้กเซต + เครื่องดื่ม 2 แก้ว');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(2, 'JE20250622002', 2, '2025-06-22', 'คุณนิดา โอน 320 บาท - กสิกรไทย', 320.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(2, 1, 320.00, 0.00, 'เงินเข้าบัญชีกสิกรไทย', 'bank_account', 1, 1),
(2, 2, 315.20, 0.00, 'ยอดสุทธิเข้าร้านกาแฟดี', 'merchant', 1, 2),
(2, 5, 4.80, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(2, 4, 0.00, 320.00, 'หนี้สินต่อร้านกาแฟดี', 'merchant', 1, 4);

-- รายการที่ 3: คุณรักษ์ โอน 89 บาท (13:45) → ไทยพาณิชย์ (Load Balance)
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(3, 2, 'BT20250622003', '2025-06-22 13:45:00', 'CREDIT', 89.00, 'คุณรักษ์ ซื้อกาแฟลาเต้', 'KTB-xxx-9012', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(3, 'TXN20250622003', 1, 3, 2, 'DEPOSIT', 89.00, 1.34, 87.66, 'COMPLETED', 'กาแฟลาเต้');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(3, 'JE20250622003', 3, '2025-06-22', 'คุณรักษ์ โอน 89 บาท - ไทยพาณิชย์', 89.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(3, 1, 89.00, 0.00, 'เงินเข้าบัญชีไทยพาณิชย์', 'bank_account', 2, 1),
(3, 2, 87.66, 0.00, 'ยอดสุทธิเข้าร้านกาแฟดี', 'merchant', 1, 2),
(3, 5, 1.34, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(3, 4, 0.00, 89.00, 'หนี้สินต่อร้านกาแฟดี', 'merchant', 1, 4);

-- รายการที่ 4: คุณแอร์ โอน 560 บาท (15:20) → ไทยพาณิชย์
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(4, 2, 'BT20250622004', '2025-06-22 15:20:00', 'CREDIT', 560.00, 'คุณแอร์ ซื้อเค้กวันเกิด + เครื่องดื่ม', 'TMB-xxx-3456', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(4, 'TXN20250622004', 1, 4, 2, 'DEPOSIT', 560.00, 8.40, 551.60, 'COMPLETED', 'เค้กวันเกิด + เครื่องดื่ม');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(4, 'JE20250622004', 4, '2025-06-22', 'คุณแอร์ โอน 560 บาท - ไทยพาณิชย์', 560.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(4, 1, 560.00, 0.00, 'เงินเข้าบัญชีไทยพาณิชย์', 'bank_account', 2, 1),
(4, 2, 551.60, 0.00, 'ยอดสุทธิเข้าร้านกาแฟดี', 'merchant', 1, 2),
(4, 5, 8.40, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(4, 4, 0.00, 560.00, 'หนี้สินต่อร้านกาแฟดี', 'merchant', 1, 4);

-- รายการที่ 5: คุณมานะ โอน 275 บาท (17:30) → กสิกรไทย
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(5, 1, 'BT20250622005', '2025-06-22 17:30:00', 'CREDIT', 275.00, 'คุณมานะ ซื้อกาแฟเย็น + ขนมหวาน', 'KBANK-xxx-7890', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(5, 'TXN20250622005', 1, 5, 1, 'DEPOSIT', 275.00, 4.13, 270.87, 'COMPLETED', 'กาแฟเย็น + ขนมหวาน');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(5, 'JE20250622005', 5, '2025-06-22', 'คุณมานะ โอน 275 บาท - กสิกรไทย', 275.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(5, 1, 275.00, 0.00, 'เงินเข้าบัญชีกสิกรไทย', 'bank_account', 1, 1),
(5, 2, 270.87, 0.00, 'ยอดสุทธิเข้าร้านกาแฟดี', 'merchant', 1, 2),
(5, 5, 4.13, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(5, 4, 0.00, 275.00, 'หนี้สินต่อร้านกาแฟดี', 'merchant', 1, 4);

-- ===================================
-- 3. รายการโอนเงินร้าน "เสื้อผ้าแฟชั่น"
-- ===================================

-- รายการที่ 6: คุณมณี โอน 1,200 บาท (11:00) → ไทยพาณิชย์
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(6, 2, 'BT20250622006', '2025-06-22 11:00:00', 'CREDIT', 1200.00, 'คุณมณี ซื้อเดรสสีฟ้า', 'SCB-xxx-2468', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(6, 'TXN20250622006', 2, 6, 2, 'DEPOSIT', 1200.00, 18.00, 1182.00, 'COMPLETED', 'เดรสสีฟ้า');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(6, 'JE20250622006', 6, '2025-06-22', 'คุณมณี โอน 1,200 บาท - ไทยพาณิชย์', 1200.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(6, 1, 1200.00, 0.00, 'เงินเข้าบัญชีไทยพาณิชย์', 'bank_account', 2, 1),
(6, 2, 1182.00, 0.00, 'ยอดสุทธิเข้าร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 2),
(6, 5, 18.00, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(6, 4, 0.00, 1200.00, 'หนี้สินต่อร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 4);

-- รายการที่ 7: คุณโจ้ โอน 850 บาท (14:30) → กรุงเทพ (Load Balance)
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(7, 3, 'BT20250622007', '2025-06-22 14:30:00', 'CREDIT', 850.00, 'คุณโจ้ ซื้อเสื้อเชิ้ต + กางเกง', 'BBL-xxx-1357', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(7, 'TXN20250622007', 2, 7, 3, 'DEPOSIT', 850.00, 12.75, 837.25, 'COMPLETED', 'เสื้อเชิ้ต + กางเกง');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(7, 'JE20250622007', 7, '2025-06-22', 'คุณโจ้ โอน 850 บาท - กรุงเทพ', 850.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(7, 1, 850.00, 0.00, 'เงินเข้าบัญชีกรุงเทพ', 'bank_account', 3, 1),
(7, 2, 837.25, 0.00, 'ยอดสุทธิเข้าร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 2),
(7, 5, 12.75, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(7, 4, 0.00, 850.00, 'หนี้สินต่อร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 4);

-- รายการที่ 8: คุณปิง โอน 2,400 บาท (16:45) → ไทยพาณิชย์
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(8, 2, 'BT20250622008', '2025-06-22 16:45:00', 'CREDIT', 2400.00, 'คุณปิง ซื้อชุดสูท', 'KTB-xxx-4680', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(8, 'TXN20250622008', 2, 8, 2, 'DEPOSIT', 2400.00, 36.00, 2364.00, 'COMPLETED', 'ชุดสูท');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(8, 'JE20250622008', 8, '2025-06-22', 'คุณปิง โอน 2,400 บาท - ไทยพาณิชย์', 2400.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(8, 1, 2400.00, 0.00, 'เงินเข้าบัญชีไทยพาณิชย์', 'bank_account', 2, 1),
(8, 2, 2364.00, 0.00, 'ยอดสุทธิเข้าร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 2),
(8, 5, 36.00, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(8, 4, 0.00, 2400.00, 'หนี้สินต่อร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 4);

-- รายการที่ 9: คุณลิซ่า โอน 680 บาท (18:15) → กรุงไทย (Load Balance)
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(9, 4, 'BT20250622009', '2025-06-22 18:15:00', 'CREDIT', 680.00, 'คุณลิซ่า ซื้อกระเป๋าแบรนด์', 'TMB-xxx-9753', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(9, 'TXN20250622009', 2, 9, 4, 'DEPOSIT', 680.00, 10.20, 669.80, 'COMPLETED', 'กระเป๋าแบรนด์');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(9, 'JE20250622009', 9, '2025-06-22', 'คุณลิซ่า โอน 680 บาท - กรุงไทย', 680.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(9, 1, 680.00, 0.00, 'เงินเข้าบัญชีกรุงไทย', 'bank_account', 4, 1),
(9, 2, 669.80, 0.00, 'ยอดสุทธิเข้าร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 2),
(9, 5, 10.20, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(9, 4, 0.00, 680.00, 'หนี้สินต่อร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 4);

-- รายการที่ 10: คุณดาว โอน 1,350 บาท (19:20) → กรุงเทพ
INSERT INTO bank_transactions (id, bank_account_id, transaction_ref, transaction_date, transaction_type, amount, description, from_account, is_processed) VALUES
(10, 3, 'BT20250622010', '2025-06-22 19:20:00', 'CREDIT', 1350.00, 'คุณดาว ซื้อชุดเดรสเซต', 'KBANK-xxx-8642', true);

INSERT INTO transactions (id, transaction_ref, merchant_id, bank_transaction_id, assigned_bank_account_id, transaction_type, gross_amount, mdr_fee, net_amount, status, description) VALUES
(10, 'TXN20250622010', 2, 10, 3, 'DEPOSIT', 1350.00, 20.25, 1329.75, 'COMPLETED', 'ชุดเดรสเซต');

INSERT INTO journal_entries (id, journal_ref, transaction_id, entry_date, description, total_amount, status) VALUES
(10, 'JE20250622010', 10, '2025-06-22', 'คุณดาว โอน 1,350 บาท - กรุงเทพ', 1350.00, 'POSTED');

INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number) VALUES
(10, 1, 1350.00, 0.00, 'เงินเข้าบัญชีกรุงเทพ', 'bank_account', 3, 1),
(10, 2, 1329.75, 0.00, 'ยอดสุทธิเข้าร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 2),
(10, 5, 20.25, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', null, 3),
(10, 4, 0.00, 1350.00, 'หนี้สินต่อร้านเสื้อผ้าแฟชั่น', 'merchant', 2, 4);

-- ===================================
-- 4. อัพเดตยอดคงเหลือ
-- ===================================

-- อัพเดตยอดบัญชีธนาคาร
UPDATE bank_accounts ba
SET current_balance = before_balance + (
    SELECT COALESCE(SUM(CASE WHEN transaction_type = 'CREDIT' THEN amount ELSE -amount END), 0)
    FROM bank_transactions bt 
    WHERE bt.bank_account_id = ba.id
),
current_daily_usage = (
    SELECT COALESCE(SUM(amount), 0)
    FROM bank_transactions bt 
    WHERE bt.bank_account_id = ba.id 
    AND DATE(bt.transaction_date) = CURDATE()
);

-- อัพเดตยอด Merchant Deposit Balance
UPDATE merchants m
SET deposit_balance = deposit_balance + (
    SELECT COALESCE(SUM(net_amount), 0)
    FROM transactions t 
    WHERE t.merchant_id = m.id 
    AND t.transaction_type = 'DEPOSIT' 
    AND t.status = 'COMPLETED'
    AND DATE(t.created_at) = CURDATE()
);

-- อัพเดตยอด Account Balances
UPDATE accounts SET current_balance = current_balance + (
    SELECT COALESCE(SUM(debit_amount) - SUM(credit_amount), 0)
    FROM journal_lines jl
    JOIN journal_entries je ON jl.journal_entry_id = je.id
    WHERE jl.account_id = accounts.id 
    AND je.status = 'POSTED'
    AND DATE(je.entry_date) = CURDATE()
);

-- ===================================
-- 5. Query ตรวจสอบผลลัพธ์
-- ===================================

-- สรุปรายการต่อร้าน
SELECT 
    m.merchant_code as 'รหัสร้าน',
    m.business_name as 'ชื่อร้าน',
    COUNT(t.id) as 'จำนวนรายการ',
    SUM(t.gross_amount) as 'ยอดโอนรวม',
    SUM(t.mdr_fee) as 'ค่าธรรมเนียมรวม',
    SUM(t.net_amount) as 'ยอดสุทธิรวม',
    m.deposit_balance as 'ยอดปัจจุบัน'
FROM merchants m
LEFT JOIN transactions t ON m.id = t.merchant_id AND t.status = 'COMPLETED'
GROUP BY m.id
ORDER BY m.merchant_code;

-- สรุปการกระจายภาระตามบัญชีธนาคาร
SELECT 
    ba.account_number as 'เลขที่บัญชี',
    ba.bank_name as 'ธนาคาร',
    ba.account_type as 'ประเภท',
    ba.before_balance as 'ยอดก่อนหน้า',
    COALESCE(SUM(bt.amount), 0) as 'เงินเข้าวันนี้',
    ba.current_balance as 'ยอดปัจจุบัน',
    ROUND((COALESCE(SUM(bt.amount), 0) / (SELECT SUM(amount) FROM bank_transactions WHERE DATE(transaction_date) = CURDATE())) * 100, 2) as 'เปอร์เซ็นต์การใช้'
FROM bank_accounts ba
LEFT JOIN bank_transactions bt ON ba.id = bt.bank_account_id AND DATE(bt.transaction_date) = CURDATE()
WHERE ba.account_type = 'DEPOSIT'
GROUP BY ba.id
ORDER BY 'เงินเข้าวันนี้' DESC;

-- ตรวจสอบ Trial Balance
SELECT 
    a.account_code as 'รหัสบัญชี',
    a.account_name as 'ชื่อบัญชี',
    SUM(jl.debit_amount) as 'Debit_รวม',
    SUM(jl.credit_amount) as 'Credit_รวม',
    SUM(jl.debit_amount) - SUM(jl.credit_amount) as 'ยอดคงเหลือ'
FROM accounts a
LEFT JOIN journal_lines jl ON a.id = jl.account_id
LEFT JOIN journal_entries je ON jl.journal_entry_id = je.id
WHERE je.status = 'POSTED'
GROUP BY a.id
HAVING SUM(jl.debit_amount) > 0 OR SUM(jl.credit_amount) > 0
ORDER BY a.account_code;

-- ตรวจสอบความสมดุล (Debit = Credit)
SELECT 
    SUM(debit_amount) as 'Total_Debit',
    SUM(credit_amount) as 'Total_Credit',
    SUM(debit_amount) - SUM(credit_amount) as 'Difference',
    CASE 
        WHEN SUM(debit_amount) - SUM(credit_amount) = 0 THEN '✅ สมดุลถูกต้อง'
        ELSE '❌ ไม่สมดุล'
    END as 'สถานะ'
FROM journal_lines jl
JOIN journal_entries je ON jl.journal_entry_id = je.id
WHERE je.status = 'POSTED';

-- สรุปรายได้ระบบ
SELECT 
    'MDR Fee Revenue' as 'ประเภทรายได้',
    SUM(mdr_fee) as 'ยอดรวม',
    COUNT(*) as 'จำนวนรายการ',
    ROUND(AVG(mdr_fee), 2) as 'ค่าเฉลี่ยต่อรายการ'
FROM transactions 
WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE();

-- รายละเอียดรายการทั้งหมดวันนี้
SELECT 
    t.transaction_ref as 'เลขที่รายการ',
    m.business_name as 'ร้านค้า',
    t.description as 'รายการ',
    t.gross_amount as 'ยอดโอน',
    t.mdr_fee as 'ค่าธรรมเนียม',
    t.net_amount as 'ยอดสุทธิ',
    ba.bank_name as 'ธนาคารที่เลือก',
    ba.account_number as 'เลขที่บัญชี',
    TIME(bt.transaction_date) as 'เวลา'
FROM transactions t
JOIN merchants m ON t.merchant_id = m.id
JOIN bank_transactions bt ON t.bank_transaction_id = bt.id
JOIN bank_accounts ba ON t.assigned_bank_account_id = ba.id
WHERE t.status = 'COMPLETED' AND DATE(t.created_at) = CURDATE()
ORDER BY bt.transaction_date;

-- ดูการจัดสรรบัญชีธนาคารของแต่ละร้าน
SELECT 
    m.merchant_code as 'รหัสร้าน',
    m.business_name as 'ชื่อร้าน',
    ba.bank_name as 'ธนาคาร',
    ba.account_number as 'เลขที่บัญชี',
    bam.account_type as 'ประเภทการใช้',
    bam.priority as 'ลำดับความสำคัญ',
    bam.weight_percent as 'สัดส่วน_%',
    CASE WHEN bam.is_active THEN '✅ ใช้งาน' ELSE '❌ ปิด' END as 'สถานะ'
FROM merchants m
JOIN bank_accounts_merchants bam ON m.id = bam.merchant_id
JOIN bank_accounts ba ON bam.bank_account_id = ba.id
ORDER BY m.merchant_code, bam.account_type, bam.priority;

-- สรุปการใช้งานแต่ละบัญชีธนาคาร (Load Balancing Report)
SELECT 
    ba.bank_name as 'ธนาคาร',
    ba.account_number as 'เลขที่บัญชี',
    ba.account_type as 'ประเภท',
    ba.current_balance as 'ยอดปัจจุบัน',
    ba.daily_limit as 'วงเงินต่อวัน',
    ba.current_daily_usage as 'ยอดใช้วันนี้',
    ROUND((ba.current_daily_usage / ba.daily_limit) * 100, 2) as 'เปอร์เซ็นต์การใช้',
    (ba.daily_limit - ba.current_daily_usage) as 'วงเงินคงเหลือ',
    COUNT(bam.id) as 'จำนวนร้านที่ใช้',
    CASE 
        WHEN (ba.current_daily_usage / ba.daily_limit) * 100 < 50 THEN '🟢 ปกติ'
        WHEN (ba.current_daily_usage / ba.daily_limit) * 100 < 80 THEN '🟡 ระวัง'
        ELSE '🔴 เกือบเต็ม'
    END as 'สถานะ'
FROM bank_accounts ba
LEFT JOIN bank_accounts_merchants bam ON ba.id = bam.bank_account_id AND bam.is_active = TRUE
WHERE ba.is_active = TRUE
GROUP BY ba.id
ORDER BY ba.account_type, 'เปอร์เซ็นต์การใช้' DESC;

-- การกระจายรายการตาม Priority และ Weight
SELECT 
    m.business_name as 'ร้านค้า',
    ba.bank_name as 'ธนาคาร',
    bam.priority as 'Priority',
    bam.weight_percent as 'Weight_%_ตั้งค่า',
    COUNT(t.id) as 'จำนวนรายการ',
    COALESCE(SUM(t.gross_amount), 0) as 'ยอดรวม',
    ROUND(
        CASE 
            WHEN (SELECT SUM(gross_amount) FROM transactions tx WHERE tx.merchant_id = m.id AND tx.status = 'COMPLETED') > 0
            THEN (COALESCE(SUM(t.gross_amount), 0) / (SELECT SUM(gross_amount) FROM transactions tx WHERE tx.merchant_id = m.id AND tx.status = 'COMPLETED')) * 100
            ELSE 0
        END, 2
    ) as 'Weight_%_จริง'
FROM merchants m
JOIN bank_accounts_merchants bam ON m.id = bam.merchant_id
JOIN bank_accounts ba ON bam.bank_account_id = ba.id
LEFT JOIN transactions t ON m.id = t.merchant_id AND t.assigned_bank_account_id = ba.id AND t.status = 'COMPLETED'
WHERE bam.account_type = 'DEPOSIT' AND bam.is_active = TRUE
GROUP BY m.id, ba.id, bam.priority
ORDER BY m.business_name, bam.priority;

-- สรุปสุดท้าย: ภาพรวมระบบ
SELECT 
    'สรุปภาพรวมระบบ GalaxyPay วันที่ 22 มิ.ย. 2567' as 'หัวข้อ';

SELECT 
    'ยอดเงินเข้าระบบทั้งหมด' as 'รายการ',
    CONCAT(FORMAT(SUM(gross_amount), 2), ' บาท') as 'ยอด'
FROM transactions 
WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE()
UNION ALL
SELECT 
    'รายได้ค่าธรรมเนียม MDR',
    CONCAT(FORMAT(SUM(mdr_fee), 2), ' บาท')
FROM transactions 
WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE()
UNION ALL
SELECT 
    'เงินสุทธิเข้าร้านค้า',
    CONCAT(FORMAT(SUM(net_amount), 2), ' บาท')
FROM transactions 
WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE()
UNION ALL
SELECT 
    'จำนวนรายการทั้งหมด',
    CONCAT(COUNT(*), ' รายการ')
FROM transactions 
WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE()
UNION ALL
SELECT 
    'จำนวนร้านค้าที่มีรายการ',
    CONCAT(COUNT(DISTINCT merchant_id), ' ร้าน')
FROM transactions 
WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE()
UNION ALL
SELECT 
    'จำนวนบัญชีธนาคารที่ใช้',
    CONCAT(COUNT(DISTINCT assigned_bank_account_id), ' บัญชี')
FROM transactions 
WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE();

-- ===================================
-- สิ้นสุด SQL Commands
-- ===================================
```