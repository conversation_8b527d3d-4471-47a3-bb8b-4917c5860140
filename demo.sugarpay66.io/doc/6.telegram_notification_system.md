# ระบบแจ้งเตือน Telegram (Telegram Notification System)
## SugarPay Payment System

---

## 📋 ภาพรวมระบบ

ระบบแจ้งเตือน Telegram ของ SugarPay มี **2 ประเภทหลัก**:

### 🔔 **1. การแจ้งเตือนอัตโนมัติ (Auto Alerts)**
- แจ้งเตือนยอดเงินหลังมีการเคลื่อนไหว
- ส่งไปยังกลุ่ม Private ของร้านค้าเอง
- ร้านค้าสามารถเปิด/ปิดการแจ้งเตือนแต่ละประเภทได้

### 📢 **2. ข้อความ Broadcast (Admin Broadcast)**
- ข้อความจากแอดมินไปยังกลุ่ม Support
- รองรับข้อความ + รูปภาพ
- ส่งไปหลายกลุ่มพร้อมกัน

---

## 🗄️ Database Schema

### 1. ตาราง telegram_alerts - การตั้งค่าการแจ้งเตือนของร้านค้า
```sql
CREATE TABLE telegram_alerts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    notify_type ENUM('DEPOSIT', 'WITHDRAW', 'TOPUP', 'TRANSFER', 'SETTLEMENT') NOT NULL COMMENT 'ประเภทการแจ้งเตือน',

    -- การตั้งค่าการแจ้งเตือน
    message_template TEXT COMMENT 'ข้อความแจ้งเตือนที่กำหนดเอง (ถ้าไม่ใส่จะใช้ template เริ่มต้น)',
    chat_id VARCHAR(50) NOT NULL COMMENT 'Chat ID ของ Telegram',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT 'เปิด/ปิดการแจ้งเตือนประเภทนี้',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_merchant_type (merchant_id, notify_type),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_notify_type (notify_type),
    INDEX idx_is_enabled (is_enabled)
) COMMENT 'ตารางการตั้งค่าการแจ้งเตือนของร้านค้า';
```

### 2. ตาราง telegram_channels - กลุ่ม Telegram สำหรับ Broadcast
```sql
CREATE TABLE telegram_channels (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'ชื่อกลุ่ม',
    chat_id VARCHAR(50) NOT NULL COMMENT 'Chat ID ของ Telegram',
    description TEXT COMMENT 'รายละเอียดกลุ่ม',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    UNIQUE KEY unique_chat_id (chat_id),
    INDEX idx_is_active (is_active)
) COMMENT 'ตารางกลุ่ม Telegram สำหรับ Broadcast';
```

### 3. ตาราง telegram_messages - ข้อความ Broadcast
```sql
CREATE TABLE telegram_messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    admin_user_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสผู้ดูแลที่สร้างข้อความ',
    title VARCHAR(200) COMMENT 'หัวข้อข้อความ',
    text TEXT NOT NULL COMMENT 'เนื้อหาข้อความ',
    image_url VARCHAR(500) COMMENT 'URL รูปภาพ (ถ้ามี)',

    -- Status
    status ENUM('DRAFT', 'SCHEDULED', 'SENDING', 'SENT', 'FAILED') DEFAULT 'DRAFT' COMMENT 'สถานะข้อความ',
    scheduled_at TIMESTAMP NULL COMMENT 'กำหนดเวลาส่ง',
    sent_at TIMESTAMP NULL COMMENT 'เวลาที่ส่งจริง',

    -- Statistics
    total_channels INT DEFAULT 0 COMMENT 'จำนวนกลุ่มที่จะส่ง',
    sent_channels INT DEFAULT 0 COMMENT 'จำนวนกลุ่มที่ส่งสำเร็จ',
    failed_channels INT DEFAULT 0 COMMENT 'จำนวนกลุ่มที่ส่งล้มเหลว',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_user_id (admin_user_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at)
) COMMENT 'ตารางข้อความ Broadcast';
```

### 4. ตาราง telegram_message_channels - ความสัมพันธ์ข้อความ ↔ กลุ่ม
```sql
CREATE TABLE telegram_message_channels (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    message_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสข้อความ',
    channel_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสกลุ่ม',

    -- Delivery Status
    status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING' COMMENT 'สถานะการส่ง',
    sent_at TIMESTAMP NULL COMMENT 'เวลาที่ส่งสำเร็จ',
    error_message TEXT COMMENT 'ข้อความข้อผิดพลาด',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (message_id) REFERENCES telegram_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (channel_id) REFERENCES telegram_channels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_channel (message_id, channel_id),
    INDEX idx_message_id (message_id),
    INDEX idx_channel_id (channel_id),
    INDEX idx_status (status)
) COMMENT 'ตารางความสัมพันธ์ข้อความ ↔ กลุ่ม';
```

---

## 📊 ข้อมูลเริ่มต้น (Initial Data)

### กลุ่ม Telegram สำหรับ Broadcast
```sql
INSERT INTO telegram_channels (id, name, chat_id, description) VALUES
(1, 'SugarPay Support', '-1001234567890', 'กลุ่มซัพพอร์ตหลัก'),
(2, 'SugarPay Announcements', '-1001234567891', 'กลุ่มประกาศข่าวสาร'),
(3, 'SugarPay Technical', '-1001234567892', 'กลุ่มเทคนิค'),
(4, 'SugarPay VIP Merchants', '-1001234567893', 'กลุ่มร้านค้า VIP');
```

### การตั้งค่าการแจ้งเตือนของร้านค้า
```sql
-- ร้านกาแฟดี - เปิดการแจ้งเตือนทุกประเภท
INSERT INTO telegram_alerts (merchant_id, notify_type, chat_id, is_enabled) VALUES
(1, 'DEPOSIT', '-1001111111111', TRUE),
(1, 'WITHDRAW', '-1001111111111', TRUE),
(1, 'TOPUP', '-1001111111111', TRUE),
(1, 'TRANSFER', '-1001111111111', FALSE), -- ปิดการแจ้งเตือน Transfer
(1, 'SETTLEMENT', '-1001111111111', TRUE);

-- ร้านเสื้อผ้าแฟชั่น - เปิดเฉพาะ Deposit และ Withdraw
INSERT INTO telegram_alerts (merchant_id, notify_type, chat_id, is_enabled) VALUES
(2, 'DEPOSIT', '-1002222222222', TRUE),
(2, 'WITHDRAW', '-1002222222222', TRUE),
(2, 'TOPUP', '-1002222222222', FALSE),
(2, 'TRANSFER', '-1002222222222', FALSE),
(2, 'SETTLEMENT', '-1002222222222', FALSE);
```

---

## 🔔 ระบบการแจ้งเตือนอัตโนมัติ

### Template ข้อความเริ่มต้น
```sql
-- Function: สร้างข้อความแจ้งเตือนเริ่มต้น
DELIMITER //
CREATE FUNCTION get_default_alert_message(
    p_notify_type VARCHAR(20),
    p_merchant_name VARCHAR(200),
    p_amount DECIMAL(15,2),
    p_transaction_ref VARCHAR(100)
) RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_message TEXT;
    DECLARE v_emoji VARCHAR(10);
    
    CASE p_notify_type
        WHEN 'DEPOSIT' THEN 
            SET v_emoji = '💰';
            SET v_message = CONCAT(v_emoji, ' เงินเข้า\n',
                                 '🏪 ร้าน: ', p_merchant_name, '\n',
                                 '💵 จำนวน: ', FORMAT(p_amount, 2), ' บาท\n',
                                 '📄 รหัส: ', p_transaction_ref, '\n',
                                 '⏰ เวลา: ', DATE_FORMAT(NOW(), '%d/%m/%Y %H:%i:%s'));
        WHEN 'WITHDRAW' THEN 
            SET v_emoji = '💸';
            SET v_message = CONCAT(v_emoji, ' เงินออก\n',
                                 '🏪 ร้าน: ', p_merchant_name, '\n',
                                 '💵 จำนวน: ', FORMAT(p_amount, 2), ' บาท\n',
                                 '📄 รหัส: ', p_transaction_ref, '\n',
                                 '⏰ เวลา: ', DATE_FORMAT(NOW(), '%d/%m/%Y %H:%i:%s'));
        WHEN 'TOPUP' THEN 
            SET v_emoji = '⬆️';
            SET v_message = CONCAT(v_emoji, ' เติมเงิน\n',
                                 '🏪 ร้าน: ', p_merchant_name, '\n',
                                 '💵 จำนวน: ', FORMAT(p_amount, 2), ' บาท\n',
                                 '📄 รหัส: ', p_transaction_ref, '\n',
                                 '⏰ เวลา: ', DATE_FORMAT(NOW(), '%d/%m/%Y %H:%i:%s'));
        WHEN 'TRANSFER' THEN 
            SET v_emoji = '🔄';
            SET v_message = CONCAT(v_emoji, ' โอนเงิน\n',
                                 '🏪 ร้าน: ', p_merchant_name, '\n',
                                 '💵 จำนวน: ', FORMAT(p_amount, 2), ' บาท\n',
                                 '📄 รหัส: ', p_transaction_ref, '\n',
                                 '⏰ เวลา: ', DATE_FORMAT(NOW(), '%d/%m/%Y %H:%i:%s'));
        WHEN 'SETTLEMENT' THEN 
            SET v_emoji = '🏦';
            SET v_message = CONCAT(v_emoji, ' ส่งเงิน\n',
                                 '🏪 ร้าน: ', p_merchant_name, '\n',
                                 '💵 จำนวน: ', FORMAT(p_amount, 2), ' บาท\n',
                                 '📄 รหัส: ', p_transaction_ref, '\n',
                                 '⏰ เวลา: ', DATE_FORMAT(NOW(), '%d/%m/%Y %H:%i:%s'));
        ELSE 
            SET v_message = CONCAT('📋 รายการธุรกรรม\n',
                                 '🏪 ร้าน: ', p_merchant_name, '\n',
                                 '💵 จำนวน: ', FORMAT(p_amount, 2), ' บาท\n',
                                 '📄 รหัส: ', p_transaction_ref, '\n',
                                 '⏰ เวลา: ', DATE_FORMAT(NOW(), '%d/%m/%Y %H:%i:%s'));
    END CASE;
    
    RETURN v_message;
END //
DELIMITER ;
```

### Trigger: ส่งการแจ้งเตือนอัตโนมัติ
```sql
-- Trigger: ส่งการแจ้งเตือนเมื่อมีรายการธุรกรรมใหม่
DELIMITER //
CREATE TRIGGER trigger_send_telegram_alert
AFTER INSERT ON transactions
FOR EACH ROW
BEGIN
    DECLARE v_merchant_name VARCHAR(200);
    DECLARE v_message TEXT;
    DECLARE v_chat_id VARCHAR(50);
    DECLARE v_is_enabled BOOLEAN DEFAULT FALSE;
    DECLARE v_custom_template TEXT;
    
    -- ดึงชื่อร้านค้า
    SELECT business_name INTO v_merchant_name
    FROM merchants 
    WHERE id = NEW.merchant_id;
    
    -- ตรวจสอบการตั้งค่าการแจ้งเตือน
    SELECT chat_id, is_enabled, message_template 
    INTO v_chat_id, v_is_enabled, v_custom_template
    FROM telegram_alerts 
    WHERE merchant_id = NEW.merchant_id 
    AND notify_type = NEW.transaction_type;
    
    -- ถ้าเปิดการแจ้งเตือน
    IF v_is_enabled THEN
        -- ใช้ template กำหนดเอง หรือ template เริ่มต้น
        IF v_custom_template IS NOT NULL THEN
            SET v_message = v_custom_template;
            -- แทนที่ placeholder
            SET v_message = REPLACE(v_message, '{merchant_name}', v_merchant_name);
            SET v_message = REPLACE(v_message, '{amount}', FORMAT(NEW.gross_amount, 2));
            SET v_message = REPLACE(v_message, '{transaction_ref}', NEW.transaction_ref);
            SET v_message = REPLACE(v_message, '{datetime}', DATE_FORMAT(NOW(), '%d/%m/%Y %H:%i:%s'));
        ELSE
            SET v_message = get_default_alert_message(NEW.transaction_type, v_merchant_name, NEW.gross_amount, NEW.transaction_ref);
        END IF;
        
        -- บันทึกลงตาราง telegram_queue สำหรับส่งภายหลัง
        INSERT INTO telegram_queue (chat_id, message, message_type, reference_type, reference_id)
        VALUES (v_chat_id, v_message, 'ALERT', 'transaction', NEW.id);
    END IF;
END //
DELIMITER ;
```

---

## 📢 ระบบ Broadcast ข้อความ

### สร้างข้อความ Broadcast
```sql
-- สร้างข้อความ Broadcast ใหม่
INSERT INTO telegram_messages (admin_user_id, title, text, image_url, status) VALUES
(1, 'ประกาศปรับปรุงระบบ', 
'🔧 แจ้งปรับปรุงระบบ SugarPay\n\n📅 วันที่: 25 มิ.ย. 2567\n⏰ เวลา: 02:00 - 04:00 น.\n\n🚀 ฟีเจอร์ใหม่:\n- ระบบรายงานแบบ Real-time\n- การแจ้งเตือน Telegram ที่ดีขึ้น\n- ปรับปรุง UI/UX\n\nขออภัยในความไม่สะดวก 🙏',
'https://example.com/images/maintenance.jpg', 'DRAFT');
```

### กำหนดกลุ่มที่จะส่ง
```sql
-- กำหนดกลุ่มที่จะส่งข้อความ
INSERT INTO telegram_message_channels (message_id, channel_id) VALUES
(1, 1), -- SugarPay Support
(1, 2), -- SugarPay Announcements
(1, 4); -- SugarPay VIP Merchants

-- อัพเดตจำนวนกลุ่มที่จะส่ง
UPDATE telegram_messages SET total_channels = 3 WHERE id = 1;
```

### ส่งข้อความ Broadcast
```sql
-- เปลี่ยนสถานะเป็น SENDING
UPDATE telegram_messages SET status = 'SENDING' WHERE id = 1;

-- อัพเดตสถานะการส่งแต่ละกลุ่ม (จะทำผ่าน Application)
UPDATE telegram_message_channels 
SET status = 'SENT', sent_at = NOW() 
WHERE message_id = 1 AND channel_id = 1;

-- อัพเดตสถิติการส่ง
UPDATE telegram_messages 
SET sent_channels = (SELECT COUNT(*) FROM telegram_message_channels WHERE message_id = 1 AND status = 'SENT'),
    failed_channels = (SELECT COUNT(*) FROM telegram_message_channels WHERE message_id = 1 AND status = 'FAILED'),
    status = 'SENT',
    sent_at = NOW()
WHERE id = 1;
```

---

## 📋 ตาราง telegram_queue - คิวการส่งข้อความ

```sql
CREATE TABLE telegram_queue (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    chat_id VARCHAR(50) NOT NULL COMMENT 'Chat ID ปลายทาง',
    message TEXT NOT NULL COMMENT 'ข้อความที่จะส่ง',
    image_url VARCHAR(500) COMMENT 'URL รูปภาพ (ถ้ามี)',

    -- ประเภทข้อความ
    message_type ENUM('ALERT', 'BROADCAST') NOT NULL COMMENT 'ประเภทข้อความ',
    reference_type VARCHAR(50) COMMENT 'ประเภทอ้างอิง เช่น transaction, message',
    reference_id BIGINT UNSIGNED COMMENT 'รหัสอ้างอิง',

    -- สถานะการส่ง
    status ENUM('PENDING', 'SENDING', 'SENT', 'FAILED') DEFAULT 'PENDING' COMMENT 'สถานะการส่ง',
    attempts INT DEFAULT 0 COMMENT 'จำนวนครั้งที่พยายามส่ง',
    max_attempts INT DEFAULT 3 COMMENT 'จำนวนครั้งสูงสุดที่จะพยายาม',

    -- เวลา
    scheduled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'เวลาที่กำหนดส่ง',
    sent_at TIMESTAMP NULL COMMENT 'เวลาที่ส่งสำเร็จ',
    failed_at TIMESTAMP NULL COMMENT 'เวลาที่ส่งล้มเหลว',

    -- ข้อผิดพลาด
    error_message TEXT COMMENT 'ข้อความข้อผิดพลาด',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_message_type (message_type),
    INDEX idx_reference (reference_type, reference_id)
) COMMENT 'ตารางคิวการส่งข้อความ Telegram';
```
