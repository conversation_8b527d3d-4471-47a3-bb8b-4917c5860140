# Payment API Documentation
## SugarPay Payment System

---

## 📋 ภาพรวม API

Payment API ของ SugarPay เป็นระบบ RESTful API ที่รองรับการทำธุรกรรมทางการเงิน ประกอบด้วย:

### 🔐 **Authentication**
- **Method:** Basic Authentication
- **Format:** `Basic base64(username:password)`
- **Header:** `Authorization: Basic dGVzdDp0ZXN0`

### 🔑 **Signature System**
ระบบใช้ Secret Key ในการเข้ารหัสข้อมูลเพื่อความปลอดภัย

---

## 🔐 Signature Generation Guide

### ขั้นตอนการสร้าง Signature:

#### 1. เพิ่ม Secret Key ลงใน Parameters
```javascript
let param = {
    "from_acc": "xxxx",
    "from_bank": "xxx", 
    "key": "Your_secret_key",
    "amount": "100.00"
}
```

#### 2. เรียงลำดับ Key ตามตัวอักษร (A-Z)
```javascript
function sortObjectByKey(obj) {
    const sortedKeys = Object.keys(obj).sort();
    const sortedObj = {};
    sortedKeys.forEach(key => {
        sortedObj[key] = obj[key];
    });
    return sortedObj;
}

let json_string = JSON.stringify(sortObjectByKey(param));
```

#### 3. เข้ารหัสด้วย MD5
```javascript
import MD5 from "crypto-js/md5";
let signature = MD5(json_string).toString();
```

#### 4. สร้าง Payload สำหรับส่ง API
```javascript
param = {
    "from_acc": "xxxx",
    "from_bank": "xxx",
    "signature": signature,
    "amount": "100.00"
}
```

---

## 💰 Settlement Conditions (เงื่อนไขการตัดรอบบัญชี)

### 🕐 **การตัดรอบอัตโนมัติ:**
- **ทุกชั่วโมง:** หากยอดฝากเกิน 50,000 บาท ระบบจะ Settlement ทันที
- **ทุกวัน 22:00 น.:** Settlement ทั้งหมดจากเมื่อวาน 22:00 ถึงวันนี้ 22:00

### 📊 **การตรวจสอบ:**
- ร้านค้าสามารถตรวจสอบยอด Settlement ได้ที่เว็บไซต์หลังบ้าน เมนู Settlement

---

## 🏦 Bank Codes (รหัสธนาคาร)

| Bank Code | Bank Name | ชื่อธนาคาร |
|-----------|-----------|-----------|
| 002 | BBL | ธนาคารกรุงเทพ |
| 004 | KBANK | ธนาคารกสิกรไทย |
| 006 | KTB | ธนาคารกรุงไทย |
| 011 | TTB | ธนาคารทหารไทยธนชาต |
| 014 | SCB | ธนาคารไทยพาณิชย์ |
| 017 | CITI | ธนาคารซิตี้แบงก์ |
| 018 | SMBC | ธนาคารซูมิโตโม มิตซุย |
| 020 | SCBT | ธนาคารสแตนดาร์ดชาร์เตอร์ด |
| 022 | CIMB | ธนาคารซีไอเอ็มบี |
| 024 | UOB | ธนาคารยูโอบี |
| 025 | BAY | ธนาคารกรุงศรีอยุธยา |
| 030 | GSB | ธนาคารออมสิน |
| 031 | HSBC | ธนาคารเอชเอสบีซี |
| 032 | DB | ธนาคารดอยซ์แบงก์ |
| 033 | GHB | ธนาคารอาคารสงเคราะห์ |
| 034 | BAAC | ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร |
| 039 | MHCB | ธนาคารมิซูโฮ คอร์ปอเรต |
| 066 | ISBT | ธนาคารอิสลามแห่งประเทศไทย |
| 067 | TISCO | ธนาคารทิสโก้ |
| 069 | KKP | ธนาคารเกียรตินาคินภัทร |
| 070 | ICBC | ธนาคารไอซีบีซี |
| 071 | TCRB | ธนาคารไทยเครดิต |
| 073 | LHBANK | ธนาคารแลนด์ แอนด์ เฮ้าส์ |
| 801 | TRUE | ทรูมันนี่ |

---

## 📱 API Endpoints

### 1. QR Deposit - สร้าง QR Code สำหรับรับเงิน

**POST:** `/v1/deposit/qrcode`

#### Request Parameters:
```json
{
    "order_id": "007",
    "amount": "100",
    "ref_name": "Customer name",
    "ref_account_no": "**********",
    "ref_bank_code": "014",
    "user_id": "username",
    "callback_url": "https://your-domain.com/callback",
    "signature": "generated_signature"
}
```

#### Response (Success):
```json
{
    "error": "0",
    "code": "200", 
    "message": "Request deposit success",
    "result": {
        "ref_id": "xxxx-xxxx-xxxx-xxxx",
        "order_id": "007",
        "amount": "100.50",
        "image": "{api_url}/deposit/image/qrcode?token=xxxx-xxxx-xxxx-xxxx",
        "timeout": {
            "days": "YYYY-MM-DD",
            "time": "HH:mm:ss", 
            "date": "2022-02-01T10:00:20.168Z"
        }
    }
}
```

### 2. Withdraw - ถอนเงิน

**POST:** `/v1/withdraw`

#### Request Parameters:
```json
{
    "order_id": "002",
    "amount": "100",
    "to_account_no": "**********",
    "to_bank_code": "KBANK",
    "to_name": "สมชาย ใจดี",
    "callback_url": "https://your-domain.com/callback",
    "signature": "generated_signature"
}
```

#### Response (Success):
```json
{
    "error": "0",
    "code": "200",
    "message": "Request withdraw success",
    "result": {
        "ref_id": "xxxx-xxxx-xxxx-xxxx",
        "order_id": "002",
        "price": "100.00"
    }
}
```

### 3. Balance - ตรวจสอบยอดเงิน

**POST:** `/v1/balance`

#### Request Parameters:
```json
{
    "bank_token": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
    "signature": "generated_signature"
}
```

#### Response:
```json
{
    "error": "0",
    "code": "200",
    "message": "Request balance success",
    "result": {
        "balance": "100.00",
        "balance_withdraw": "0.00",
        "date": "2023-02-01T10:11:43+07:00"
    }
}
```

### 4. Order Inquiry - ตรวจสอบสถานะรายการ

**POST:** `/v1/order/inquiry`

#### Request Parameters:
```json
{
    "order_id": "003",
    "ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx"
}
```

### 5. Bank List - รายการธนาคาร

**GET:** `/v1/bank_list`

#### Response:
```json
{
    "error": "0",
    "code": "200",
    "message": "OK",
    "result": [
        {
            "bank_type": "DEPOSIT",
            "bank_token": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
            "bank_name": "KBANK",
            "bank_code": "004",
            "bank_account_no": "**********",
            "bank_account_name": "สมชาย ใจดี"
        }
    ]
}
```

### 6. Bank Statement - รายการเคลื่อนไหวบัญชี

**POST:** `/v1/bank_statement`

#### Request Parameters:
```json
{
    "date": "YYYY-MM-DD",
    "bank_token": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
    "signature": "generated_signature"
}
```

### 7. Request Callback - ขอส่ง Callback ซ้ำ

**POST:** `/v1/request_callback`

#### Request Parameters:
```json
{
    "ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
    "order_id": "ORDERID",
    "signature": "generated_signature"
}
```

### 8. Scan Slip Verify - ตรวจสอบสลิป

**POST:** `/v1/scan_slip_verify`

#### Request Parameters:
```json
{
    "qrtext": "string",
    "url_callback_slip": "string", 
    "signature": "generated_signature"
}
```

### 9. Scan Slip Confirm - ยืนยันสลิป

**POST:** `/v1/scan_slip_confirm`

#### Request Parameters:
```json
{
    "order_hash": "string",
    "username": "string",
    "remark": "string",
    "callback_url": "string",
    "signature": "generated_signature"
}
```

---

## 🔔 Callback Events

### Deposit Callback

#### Success Event:
```json
{
    "type": "DEPOSIT",
    "status": "SUCCESS",
    "status_code": "DEPOSIT_AUTO",
    "agent_confirm": "CONFIRM",
    "stm_ref_id": "d6c623ab-d62e812c-5597a81f-7bca944a",
    "stm_date": "YYYY-MM-DD hh:mm:ss",
    "stm_amount": "100.50",
    "stm_bank_name": "SCB",
    "stm_account_no": "**********",
    "stm_remark": "TR fr 004-********** โอนเงินเข้าพร้อมเพย์",
    "txn_ref_id": "ca3c3757-ffa8-49db-89df-e314cc5ecf60",
    "txn_order_id": "xxxxxxxx",
    "txn_user_id": "xxxxxxxx",
    "deposit_balance": "100.00",
    "withdraw_balance": "0.00",
    "remark": "",
    "signature": "d95ba17d99c862a44ebb6f1c3039e6b4"
}
```

### Withdraw Callback

#### Success Event:
```json
{
    "type": "WITHDRAW",
    "status": "SUCCESS",
    "status_code": "OK",
    "stm_ref_id": "",
    "stm_date": "2023-10-27 14:00:00",
    "stm_amount": "1000.00",
    "stm_bank_name": "",
    "stm_bank_code": "",
    "stm_last_4account": "",
    "stm_remark": "",
    "txn_ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
    "txn_order_id": "xxxxxxxx",
    "txn_user_id": "",
    "timestamp": "2023-10-27 14:02:46",
    "account_no": "**********",
    "account_bank_name": "SCB",
    "agent_confirm": "",
    "stm_account_no": "",
    "deposit_balance": "1.00",
    "withdraw_balance": "1.00",
    "remark": "",
    "signature": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

#### Failed Event:
```json
{
    "type": "WITHDRAW",
    "status": "FAILED",
    "status_code": "BANK_TRANSFER_FAIL",
    "stm_ref_id": "",
    "stm_date": "2023-10-27 14:00:00",
    "stm_amount": "1000.00",
    "stm_bank_name": "",
    "stm_bank_code": "",
    "stm_last_4account": "",
    "stm_remark": "",
    "txn_ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
    "txn_order_id": "xxxxxxxx",
    "txn_user_id": "",
    "timestamp": "2023-10-27 14:02:46",
    "account_no": "**********",
    "account_bank_name": "SCB",
    "agent_confirm": "",
    "stm_account_no": "",
    "deposit_balance": "1.00",
    "withdraw_balance": "1.00",
    "remark": "",
    "signature": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
}
```

---

## 📊 Status Codes

### Deposit Status Codes:
- **DEPOSIT_NOT_MATCH:** จับคู่รายการ QR Code ไม่ได้
- **DEPOSIT_AUTO:** จับคู่รายการ QR Code เรียบร้อย
- **DEPOSIT_MATCH:** จับคู่รายการ QR Code เรียบร้อย
- **DEPOSIT_AUTO_CUSTOMER:** จับคู่รายการจากเลขบัญชีลูกค้าที่มีในระบบได้
- **TIMEOUT:** QR Code หมดอายุการใช้งาน
- **CUST_CREATE_NEW_QR:** ลูกค้าสร้าง QR Code ใหม่

### Withdraw Status Codes:
- **OK:** ถอนเงินสำเร็จ
- **BANK_TRANSFER_FAIL:** การโอนเงินผ่านธนาคารล้มเหลว
- **INSUFFICIENT_BALANCE:** ยอดเงินไม่เพียงพอสำหรับการถอน
- **INVALID_ACCOUNT:** บัญชีปลายทางไม่ถูกต้อง

---

## ⚠️ Error Responses

### 400 Bad Request:
```json
{
    "error": "1",
    "code": "400",
    "message": "Bad Request",
    "result": {}
}
```

### 401 Unauthorized:
```json
{
    "error": "1",
    "code": "401",
    "message": "Unauthorized. Please check you API_KEY or you signature is invalid.",
    "result": {}
}
```

---

## 🔧 Implementation Examples

### PHP Example:
```php
<?php
// Signature Generation
$param = array(
    "order_id" => "007",
    "amount" => "100",
    "key" => "Your_secret_key"
);

ksort($param); // Sort by key
$json_string = json_encode($param, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
$signature = md5($json_string);

// Remove key and add signature
unset($param['key']);
$param['signature'] = $signature;

// Send to API
$curl = curl_init();
curl_setopt_array($curl, array(
    CURLOPT_URL => 'https://xxxx.io/v1/deposit/qrcode',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($param),
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json',
        'Authorization: Basic ' . base64_encode('username:password')
    ),
));

$response = curl_exec($curl);
curl_close($curl);
?>
```

### JavaScript Example:
```javascript
import MD5 from "crypto-js/md5";

// Signature Generation
function sortObjectByKey(obj) {
    const sortedKeys = Object.keys(obj).sort();
    const sortedObj = {};
    sortedKeys.forEach(key => {
        sortedObj[key] = obj[key];
    });
    return sortedObj;
}

let param = {
    "order_id": "007",
    "amount": "100",
    "key": "Your_secret_key"
};

let json_string = JSON.stringify(sortObjectByKey(param));
let signature = MD5(json_string).toString();

// Remove key and add signature
delete param.key;
param.signature = signature;

// Send to API
fetch('https://xxxx.io/v1/deposit/qrcode', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Basic ' + btoa('username:password')
    },
    body: JSON.stringify(param)
})
.then(response => response.json())
.then(data => console.log(data));
```

---

## 📝 หมายเหตุการใช้งาน

### ✅ **ข้อควรระวัง:**
- ตรวจสอบ Signature ให้ถูกต้องทุกครั้ง
- เก็บ Secret Key ให้ปลอดภัย
- ใช้ HTTPS เท่านั้น
- ตรวจสอบ Callback signature ก่อนประมวลผล

### 🔄 **Rate Limiting:**
- จำกัดการเรียก API ตามเงื่อนไขของแต่ละ endpoint
- ใช้ Request Callback หากต้องการ callback ซ้ำ

### 📞 **Support:**
- ติดต่อทีมพัฒนาหากมีปัญหาการใช้งาน
- ตรวจสอบเอกสารล่าสุดที่ payment-api-doc.readme.io

ระบบ Payment API นี้ครอบคลุมการทำธุรกรรมทางการเงินอย่างครบถ้วนและปลอดภัย! 🚀
