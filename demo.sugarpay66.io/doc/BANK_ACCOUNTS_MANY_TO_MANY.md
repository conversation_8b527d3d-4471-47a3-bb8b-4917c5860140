# การออกแบบ Bank Accounts แบบ Many-to-Many

## ภาพรวมการเปลี่ยนแปลง

เปลี่ยนจากโครงสร้างเดิม (One-to-Many) เป็น **Many-to-Many** ระหว่าง merchants และ bank_accounts

### เหตุผลการเปลี่ยนแปลง
- **ประหยัดต้นทุน**: บัญชีธนาคารหนึ่งบัญชีใช้ได้กับหลายร้านค้า
- **จัดการง่าย**: ไม่ต้องเปิดบัญชีใหม่สำหรับทุกร้านค้า
- **ยืดหยุ่น**: ร้านค้าสามารถใช้บัญชีร่วมกันได้
- **ขยายตัว**: รองรับการเติบโตของธุรกิจ

---

## โครงสร้างตารางใหม่

### 1. ตาราง `bank_accounts` (Master)
เก็บข้อมูลบัญชีธนาคารหลัก ไม่ผูกกับร้านค้าใดเฉพาะ

```sql
CREATE TABLE bank_accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    bank_code VARCHAR(10) NOT NULL COMMENT 'รหัสธนาคาร',
    bank_name VARCHAR(100) NOT NULL COMMENT 'ชื่อธนาคาร',
    account_no VARCHAR(20) NOT NULL UNIQUE COMMENT 'เลขที่บัญชี',
    account_name VARCHAR(200) NOT NULL COMMENT 'ชื่อบัญชี',
    account_type ENUM('DEPOSIT', 'WITHDRAW', 'SAVINGS') NOT NULL COMMENT 'ประเภทการใช้งานสำหรับร้านค้านี้',

    -- API Integration with Bank
    api_key VARCHAR(255) NOT NULL UNIQUE COMMENT 'API Key สำหรับเชื่อมต่อ',
    secret_key VARCHAR(255) NOT NULL COMMENT 'Secret Key สำหรับเชื่อมต่อ',
    api_endpoint VARCHAR(500) COMMENT 'API Endpoint ของลูกค้า',
    callback_url VARCHAR(500) COMMENT 'Default Callback URL',
    ip_whitelist TEXT COMMENT 'IP Whitelist สำหรับธนาคาร',

    -- Balance and Settings
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินปัจจุบันในบัญชี (อัปเดตจาก log_bank_entries)',
    daily_limit DECIMAL(15,2) COMMENT 'วงเงินรายวัน',
    metadata JSON COMMENT 'ข้อมูลเพิ่มเติม',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    INDEX idx_bank_code (bank_code),
    INDEX idx_account_no (account_no),
    INDEX idx_account_type (account_type),
    INDEX idx_is_active (is_active)
) COMMENT 'ตารางข้อมูลบัญชีธนาคาร (Master) - บัญชีหนึ่งใช้ได้กับหลายร้านค้า';
```

### 2. ตาราง `merchant_bank_accounts` (Junction Table)
เชื่อมโยงระหว่าง merchants และ bank_accounts

```sql
CREATE TABLE merchant_bank_accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL,
    bank_account_id BIGINT UNSIGNED NOT NULL,
    account_type ENUM('DEPOSIT', 'WITHDRAW', 'SAVINGS') NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    merchant_daily_limit DECIMAL(15,2),
    priority_order INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

---

## ตัวอย่างการใช้งาน

### ข้อมูลตัวอย่าง

#### Bank Accounts (Master)
| ID | Bank | Account No | Account Name |
|----|------|------------|--------------|
| 1 | SCB | ********** | บริษัท วิวัฒน์ อินดีไซน์ จำกัด |
| 2 | KBANK | ********** | บริษัท ซูเปอร์ทรีบิ้วตี้สโตร์ จำกัด |
| 9 | SCB | ********** | Shared SCB Account |
| 10 | KBANK | ********** | Shared KBANK Account |

#### Merchant Bank Accounts (Relationships)
| Merchant | Bank Account | Type | Primary | Priority |
|----------|--------------|------|---------|----------|
| Tiger Shop | SCB (**********) | DEPOSIT | ✓ | 1 |
| Tiger Shop | KBANK (**********) | DEPOSIT | ✗ | 2 |
| Tiger Shop | Shared SCB (**********) | DEPOSIT | ✗ | 3 |
| Lion Store | KBANK (**********) | DEPOSIT | ✓ | 1 |
| Lion Store | Shared KBANK (**********) | DEPOSIT | ✗ | 2 |
| Lion Store | Shared SCB (**********) | WITHDRAW | ✗ | 2 |

### ตัวอย่างการ Query

#### 1. ดูบัญชี DEPOSIT ทั้งหมดของร้านค้า
```sql
SELECT 
    ba.bank_name,
    ba.account_no,
    ba.account_name,
    mba.is_primary,
    mba.is_active,
    mba.priority_order,
    mba.merchant_daily_limit
FROM merchant_bank_accounts mba
JOIN bank_accounts ba ON mba.bank_account_id = ba.id
WHERE mba.merchant_id = 1 
AND mba.account_type = 'DEPOSIT'
AND mba.is_active = TRUE
ORDER BY mba.priority_order ASC;
```

#### 2. หาบัญชีที่ใช้ร่วมกันระหว่างร้านค้า
```sql
SELECT 
    ba.account_no,
    ba.account_name,
    COUNT(mba.merchant_id) as merchant_count,
    GROUP_CONCAT(m.merchant_name) as merchants
FROM bank_accounts ba
JOIN merchant_bank_accounts mba ON ba.id = mba.bank_account_id
JOIN merchants m ON mba.merchant_id = m.id
GROUP BY ba.id
HAVING merchant_count > 1;
```

#### 3. เลือกบัญชีสำหรับทำรายการ (Algorithm)
```sql
SELECT 
    mba.id as merchant_bank_account_id,
    ba.account_no,
    ba.bank_name,
    mba.priority_order
FROM merchant_bank_accounts mba
JOIN bank_accounts ba ON mba.bank_account_id = ba.id
WHERE mba.merchant_id = ? 
AND mba.account_type = 'DEPOSIT'
AND mba.is_active = TRUE
AND ba.is_active = TRUE
AND (mba.merchant_daily_limit IS NULL OR ba.balance < mba.merchant_daily_limit)
ORDER BY mba.priority_order ASC, mba.is_primary DESC
LIMIT 1;
```

---

## ข้อดีของการออกแบบใหม่

### 1. **ประหยัดต้นทุน**
- บัญชีหนึ่งใช้ได้กับหลายร้านค้า
- ลดค่าใช้จ่ายในการเปิดบัญชีใหม่
- ใช้ทรัพยากรร่วมกันอย่างมีประสิทธิภาพ

### 2. **ยืดหยุ่นในการจัดการ**
- เพิ่ม-ลดร้านค้าได้โดยไม่กระทบบัญชี
- ปรับเปลี่ยนการใช้งานบัญชีได้ตามต้องการ
- กำหนด priority และ limit แยกต่างหากได้

### 3. **รองรับการขยายตัว**
- เพิ่มร้านค้าใหม่ได้ง่าย
- ใช้บัญชีที่มีอยู่แล้วได้ทันที
- ไม่ต้องรอการอนุมัติบัญชีใหม่

### 4. **การจัดการความเสี่ยง**
- กระจายความเสี่ยงระหว่างร้านค้า
- หากร้านหนึ่งมีปัญหา ไม่กระทบร้านอื่น
- สามารถปิดการใช้งานเฉพาะร้านได้

---

## การจัดการ Priority และ Load Balancing

### Priority System
- **Priority 1**: บัญชีหลัก (ใช้เป็นอันดับแรก)
- **Priority 2**: บัญชีรอง (ใช้เมื่อบัญชีหลักไม่พร้อม)
- **Priority 3**: บัญชีสำรอง (ใช้เมื่อจำเป็น)

### Load Balancing Algorithm
1. เรียงตาม `priority_order` (น้อยไปมาก)
2. ตรวจสอบ `is_primary` (TRUE ก่อน)
3. ตรวจสอบ `is_active` (TRUE เท่านั้น)
4. ตรวจสอบ `merchant_daily_limit`
5. ตรวจสอบ `balance` ในบัญชี

### ตัวอย่าง Algorithm
```php
function selectBankAccount($merchantId, $accountType, $amount) {
    $sql = "
        SELECT mba.id, ba.account_no, ba.balance, mba.merchant_daily_limit
        FROM merchant_bank_accounts mba
        JOIN bank_accounts ba ON mba.bank_account_id = ba.id
        WHERE mba.merchant_id = ? 
        AND mba.account_type = ?
        AND mba.is_active = TRUE
        AND ba.is_active = TRUE
        ORDER BY mba.priority_order ASC, mba.is_primary DESC
    ";
    
    $accounts = executeQuery($sql, [$merchantId, $accountType]);
    
    foreach ($accounts as $account) {
        // ตรวจสอบวงเงิน
        if ($account['merchant_daily_limit'] && 
            $account['balance'] + $amount > $account['merchant_daily_limit']) {
            continue;
        }
        
        // ตรวจสอบยอดคงเหลือ
        if ($accountType === 'WITHDRAW' && $account['balance'] < $amount) {
            continue;
        }
        
        return $account;
    }
    
    throw new Exception('No suitable bank account found');
}
```

---

## การ Migration จากโครงสร้างเดิม

### ขั้นตอนการ Migrate
1. **สร้างตารางใหม่**: `bank_accounts` และ `merchant_bank_accounts`
2. **ย้ายข้อมูล**: จากตาราง `bank_accounts` เดิม
3. **อัปเดต Foreign Keys**: ในตาราง `transactions`
4. **ทดสอบระบบ**: ตรวจสอบการทำงาน
5. **ลบตารางเดิม**: หลังจากมั่นใจแล้ว

### Script Migration
```sql
-- 1. ย้ายข้อมูลบัญชีธนาคาร
INSERT INTO bank_accounts (bank_code, bank_name, account_no, account_name, ...)
SELECT DISTINCT bank_code, bank_name, account_no, account_name, ...
FROM old_bank_accounts;

-- 2. สร้างความสัมพันธ์
INSERT INTO merchant_bank_accounts (merchant_id, bank_account_id, account_type, ...)
SELECT oba.merchant_id, ba.id, oba.account_type, ...
FROM old_bank_accounts oba
JOIN bank_accounts ba ON oba.account_no = ba.account_no;

-- 3. อัปเดต transactions
UPDATE transactions t
JOIN old_bank_accounts oba ON t.bank_account_id = oba.id
JOIN merchant_bank_accounts mba ON oba.merchant_id = mba.merchant_id 
    AND oba.account_no = (SELECT account_no FROM bank_accounts WHERE id = mba.bank_account_id)
SET t.merchant_bank_account_id = mba.id;
```

---

## สรุป

การเปลี่ยนแปลงนี้ทำให้ระบบ SugarPay มีความยืดหยุ่นและประหยัดต้นทุนมากขึ้น โดยยังคงรักษาความปลอดภัยและการควบคุมที่เข้มงวด

### ประโยชน์หลัก
- ✅ ประหยัดต้นทุนการเปิดบัญชี
- ✅ ยืดหยุ่นในการจัดการ
- ✅ รองรับการขยายตัว
- ✅ กระจายความเสี่ยง
- ✅ ใช้ทรัพยากรร่วมกันอย่างมีประสิทธิภาพ
