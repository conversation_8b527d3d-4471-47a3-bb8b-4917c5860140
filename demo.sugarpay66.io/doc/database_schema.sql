-- =====================================================
-- SugarPay Database Schema
-- ระบบ Payment สำหรับ Merchant และ Backoffice
-- =====================================================

-- 1. ตาราง users - ข้อมูลผู้ใช้งานระบบ
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'ชื่อผู้ใช้',
    email VARCHAR(100) UNIQUE COMMENT 'อีเมล',
    password VARCHAR(255) NOT NULL COMMENT 'รหัสผ่าน (encrypted)',
    first_name VARCHAR(100) COMMENT 'ชื่อจริง',
    last_name VARCHAR(100) COMMENT 'นามสกุล',
    phone VARCHAR(20) COMMENT 'เบอร์โทรศัพท์',
    user_type ENUM('ADMIN', 'MERCHANT', 'SUB_USER') NOT NULL DEFAULT 'MERCHANT' COMMENT 'ประเภทผู้ใช้',
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') NOT NULL DEFAULT 'ACTIVE' COMMENT 'สถานะการใช้งาน',
    google2fa_secret VARCHAR(255) COMMENT 'Google 2FA Secret Key',
    is_google2fa_enabled BOOLEAN DEFAULT FALSE COMMENT 'เปิดใช้งาน 2FA',
    last_login_at TIMESTAMP NULL COMMENT 'เข้าสู่ระบบครั้งล่าสุด',
    password_changed_at TIMESTAMP NULL COMMENT 'เปลี่ยนรหัสผ่านครั้งล่าสุด',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข'
) COMMENT 'ตารางข้อมูลผู้ใช้งานระบบ';

-- 2. ตาราง merchants - ข้อมูลร้านค้า
CREATE TABLE merchants (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสผู้ใช้',
    merchant_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'รหัสร้านค้า',
    merchant_name VARCHAR(200) NOT NULL COMMENT 'ชื่อร้านค้า',
    business_type VARCHAR(100) COMMENT 'ประเภทธุรกิจ',
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') NOT NULL DEFAULT 'ACTIVE' COMMENT 'สถานะการใช้งาน',
    
    -- API Integration
    api_key VARCHAR(255) NOT NULL UNIQUE COMMENT 'API Key สำหรับเชื่อมต่อ',
    secret_key VARCHAR(255) NOT NULL COMMENT 'Secret Key สำหรับเชื่อมต่อ',
    api_endpoint VARCHAR(500) COMMENT 'API Endpoint ของลูกค้า',
    callback_url VARCHAR(500) COMMENT 'Default Callback URL',
    ip_whitelist TEXT COMMENT 'IP Whitelist (JSON format)',
    
    -- MDR Rates
    deposit_mdr_rate DECIMAL(5,2) DEFAULT 1.50 COMMENT 'อัตรา MDR สำหรับฝาก (%)',
    withdraw_mdr_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT 'อัตรา MDR สำหรับถอน (%)',
    withdraw_mdr_fixed DECIMAL(10,2) DEFAULT 10.00 COMMENT 'ค่าธรรมเนียมถอนคงที่ (THB)',
    settlement_mdr_fixed DECIMAL(10,2) DEFAULT 10.00 COMMENT 'ค่าธรรมเนียม Settlement คงที่ (THB)',
    
    -- Settings
    withdraw_pin_code VARCHAR(255) COMMENT 'PIN Code สำหรับถอนเงิน (encrypted)',
    is_auto_cancel_withdraw BOOLEAN DEFAULT FALSE COMMENT 'ยกเลิกรายการถอนอัตโนมัติเมื่อปิดระบบ',
    
    -- Telegram Notification
    telegram_bot_token VARCHAR(255) COMMENT 'Token ของ Telegram Bot',
    telegram_chat_id VARCHAR(100) COMMENT 'Chat ID ของ Telegram',

    -- Bank Account Info
    bank_code VARCHAR(10) COMMENT 'รหัสธนาคาร',
    bank_name VARCHAR(100) COMMENT 'ชื่อธนาคาร',
    bank_account_no VARCHAR(20) COMMENT 'เลขที่บัญชี',
    bank_account_name VARCHAR(200) COMMENT 'ชื่อบัญชี',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) COMMENT 'ตารางข้อมูลร้านค้า';

-- 3. ตาราง merchant_balances - ยอดเงินของร้านค้า
CREATE TABLE merchant_balances (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    -- Balances
    deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินฝาก (จากการรับชำระ)',
    withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินถอน (พร้อมถอนได้)',
    frozen_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินที่ถูกระงับ',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_merchant_balance (merchant_id)
) COMMENT 'ตารางยอดเงินของร้านค้า';

-- 4. ตาราง bank_accounts - ข้อมูลบัญชีธนาคาร (Master)
CREATE TABLE bank_accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    bank_code VARCHAR(10) NOT NULL COMMENT 'รหัสธนาคาร',
    bank_name VARCHAR(100) NOT NULL COMMENT 'ชื่อธนาคาร',
    account_no VARCHAR(20) NOT NULL UNIQUE COMMENT 'เลขที่บัญชี',
    account_name VARCHAR(200) NOT NULL COMMENT 'ชื่อบัญชี',
    account_type ENUM('DEPOSIT', 'WITHDRAW', 'SAVINGS') NOT NULL COMMENT 'ประเภทการใช้งานสำหรับร้านค้านี้',

    -- API Integration with Bank
    api_key VARCHAR(255) NOT NULL UNIQUE COMMENT 'API Key สำหรับเชื่อมต่อ',
    secret_key VARCHAR(255) NOT NULL COMMENT 'Secret Key สำหรับเชื่อมต่อ',
    api_endpoint VARCHAR(500) COMMENT 'API Endpoint ของลูกค้า',
    callback_url VARCHAR(500) COMMENT 'Default Callback URL',
    ip_whitelist TEXT COMMENT 'IP Whitelist สำหรับธนาคาร',

    -- Balance and Settings
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเงินปัจจุบันในบัญชี',
    daily_limit DECIMAL(15,2) COMMENT 'วงเงินรายวัน',
    metadata JSON COMMENT 'ข้อมูลเพิ่มเติม',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    INDEX idx_bank_code (bank_code),
    INDEX idx_account_no (account_no),
    INDEX idx_account_type (account_type),
    INDEX idx_is_active (is_active)
) COMMENT 'ตารางข้อมูลบัญชีธนาคาร (Master) - บัญชีหนึ่งใช้ได้กับหลายร้านค้า';

-- 6. ตาราง merchant_bank_accounts - ความสัมพันธ์ระหว่างร้านค้าและบัญชีธนาคาร
CREATE TABLE merchant_bank_accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    bank_account_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสบัญชีธนาคาร',

    account_type ENUM('DEPOSIT', 'WITHDRAW', 'SAVINGS') NOT NULL COMMENT 'ประเภทการใช้งานสำหรับร้านค้านี้',

    -- Settings per Merchant
    is_primary BOOLEAN DEFAULT FALSE COMMENT 'บัญชีหลักสำหรับร้านค้านี้',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งานสำหรับร้านค้านี้',
    priority_order INT DEFAULT 1 COMMENT 'ลำดับความสำคัญ (1 = สูงสุด)',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่เชื่อมโยง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_merchant_bank_type (merchant_id, bank_account_id, account_type),
    INDEX idx_merchant_type (merchant_id, account_type),
    INDEX idx_bank_account (bank_account_id),
    INDEX idx_priority (merchant_id, account_type, priority_order)
) COMMENT 'ตารางความสัมพันธ์ระหว่างร้านค้าและบัญชีธนาคาร (Many-to-Many)';

-- 7. ตาราง transactions - รายการธุรกรรม
CREATE TABLE transactions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    bank_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสบัญชีธนาคาร',
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    order_id VARCHAR(100) NOT NULL COMMENT 'รหัสคำสั่งซื้อ',
    transaction_type ENUM('DEPOSIT', 'WITHDRAW', 'TOPUP', 'TRANSFER', 'SETTLEMENT') NOT NULL COMMENT 'ประเภทรายการ',
    
    -- Amount Information
    amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงิน',
    mdr_fee DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียม MDR',
    net_amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงินสุทธิ',
    
    -- Status
    status ENUM('CREATE', 'ON_PROCESS', 'WAIT_CONFIRM', 'SUCCESS', 'REFUSE', 'BLACKLIST', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'CREATE' COMMENT 'สถานะรายการ',
    
    -- Bank Information
    merchant_bank_account_id BIGINT UNSIGNED COMMENT 'รหัสความสัมพันธ์ร้านค้า-บัญชีธนาคาร',
    bank_ref_no VARCHAR(100) COMMENT 'เลขที่อ้างอิงธนาคาร',
    customer_bank_account_no VARCHAR(20) COMMENT 'เลขบัญชีลูกค้า',
    customer_bank_account_name VARCHAR(200) COMMENT 'ชื่อบัญชีลูกค้า',
    customer_bank_code VARCHAR(10) COMMENT 'รหัสธนาคารลูกค้า',
    
    -- API Information
    channel ENUM('WEB', 'API') DEFAULT 'API' COMMENT 'ช่องทางการทำรายการ',
    callback_url VARCHAR(500) COMMENT 'Callback URL',
    api_response TEXT COMMENT 'API Response (JSON)',
    
    -- Slip Information
    slip_image_url VARCHAR(500) COMMENT 'URL รูปสลิป',
    slip_verified_at TIMESTAMP NULL COMMENT 'วันที่ตรวจสอบสลิป',
    
    -- Timestamps
    transaction_at TIMESTAMP NULL COMMENT 'วันที่ทำรายการจริง',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',
    
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (merchant_bank_account_id) REFERENCES merchant_bank_accounts(id) ON DELETE SET NULL,
    UNIQUE KEY unique_merchant_order (merchant_id, order_id),
    INDEX idx_status (status),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_transaction_at (transaction_at),
    INDEX idx_created_at (created_at)
) COMMENT 'ตารางรายการธุรกรรม';

-- 8. ตาราง bank_transactions - รายการ ทำงานพร้อม transactions

CREATE TABLE bank_transactions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'รหัสหลัก',
    bank_account_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสบัญชีธนาคาร',
    transaction_id BIGINT UNSIGNED NULL COMMENT 'รหัสธุรกรรมที่เกี่ยวข้อง (ถ้ามี)',
    transaction_at TIMESTAMP NOT NULL COMMENT 'วันที่ธุรกรรม',
    transaction_type ENUM('DEPOSIT', 'WITHDRAW', 'TOPUP', 'SETTLEMENT') NOT NULL COMMENT 'ประเภทรายการ',
    amount DECIMAL(15,2) NOT NULL COMMENT 'จำนวนเงิน',
    before_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดก่อนธุรกรรม',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดหลังธุรกรรม',
    description TEXT COMMENT 'รายละเอียดเพิ่มเติม',
    from_account VARCHAR(50) COMMENT 'บัญชีต้นทาง',
    to_account VARCHAR(50) COMMENT 'บัญชีปลายทาง',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id) ON DELETE RESTRICT,

    INDEX idx_bank_account_id (bank_account_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_amount (amount),
    INDEX idx_created_at (created_at),
    INDEX idx_transaction_at (transaction_at),
    INDEX idx_transaction_type (transaction_type)
) COMMENT 'รายการธุรกรรมธนาคาร';

-- 8. ตาราง user_groups - กลุ่มผู้ใช้
CREATE TABLE user_groups (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'ชื่อกลุ่ม',
    description TEXT COMMENT 'รายละเอียดกลุ่ม',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข'
) COMMENT 'ตารางกลุ่มผู้ใช้';

-- 9. ตาราง permissions - สิทธิ์การใช้งาน
CREATE TABLE permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'ชื่อสิทธิ์',
    permission_key VARCHAR(100) NOT NULL UNIQUE COMMENT 'Key สิทธิ์',
    description TEXT COMMENT 'รายละเอียดสิทธิ์',
    module VARCHAR(50) COMMENT 'โมดูล',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง'
) COMMENT 'ตารางสิทธิ์การใช้งาน';

-- 10. ตาราง group_permissions - สิทธิ์ของกลุ่ม
CREATE TABLE group_permissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    group_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสกลุ่ม',
    permission_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสสิทธิ์',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_group_permission (group_id, permission_id)
) COMMENT 'ตารางสิทธิ์ของกลุ่ม';

-- 11. ตาราง user_group_members - สมาชิกในกลุ่ม
CREATE TABLE user_group_members (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสผู้ใช้',
    group_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสกลุ่ม',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_group (user_id, group_id)
) COMMENT 'ตารางสมาชิกในกลุ่ม';

-- 12. ตาราง blacklist - รายชื่อดำ
CREATE TABLE blacklist (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    blacklist_type ENUM('BANK_ACCOUNT', 'PHONE', 'EMAIL', 'IP') NOT NULL COMMENT 'ประเภทรายชื่อดำ',
    blacklist_value VARCHAR(200) NOT NULL COMMENT 'ค่าที่ถูกบล็อก',
    reason TEXT COMMENT 'เหตุผล',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    INDEX idx_blacklist_type (blacklist_type),
    INDEX idx_blacklist_value (blacklist_value)
) COMMENT 'ตารางรายชื่อดำ';

-- 13. ตาราง scan_slip_tasks - งานสแกนสลิป
CREATE TABLE scan_slip_tasks (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    batch_id VARCHAR(100) NOT NULL COMMENT 'รหัส Batch',
    slip_image_url VARCHAR(500) NOT NULL COMMENT 'URL รูปสลิป',

    -- Extracted Information
    bank_code VARCHAR(10) COMMENT 'รหัสธนาคาร',
    account_no VARCHAR(20) COMMENT 'เลขที่บัญชี',
    amount DECIMAL(15,2) COMMENT 'จำนวนเงิน',
    transaction_at TIMESTAMP NULL COMMENT 'วันที่ทำรายการ',
    reference_no VARCHAR(100) COMMENT 'เลขที่อ้างอิง',

    -- Status
    status ENUM('IMPORTED', 'PROCESSING', 'VERIFIED', 'MATCHED', 'FAILED') DEFAULT 'IMPORTED' COMMENT 'สถานะ',
    matched_transaction_id BIGINT UNSIGNED COMMENT 'รหัสรายการที่จับคู่',

    -- Processing Info
    processed_at TIMESTAMP NULL COMMENT 'วันที่ประมวลผล',
    verified_at TIMESTAMP NULL COMMENT 'วันที่ตรวจสอบ',
    error_message TEXT COMMENT 'ข้อความข้อผิดพลาด',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (matched_transaction_id) REFERENCES transactions(id) ON DELETE SET NULL,
    INDEX idx_batch_id (batch_id),
    INDEX idx_status (status),
    INDEX idx_account_no (account_no)
) COMMENT 'ตารางงานสแกนสลิป';

-- 14. ตาราง daily_reports - รายงานรายวัน
CREATE TABLE daily_reports (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    report_date DATE NOT NULL COMMENT 'วันที่รายงาน',

    -- Deposit Summary
    total_deposit_count INT DEFAULT 0 COMMENT 'จำนวนรายการฝาก',
    total_deposit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดรวมฝาก',
    total_deposit_mdr DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมฝากรวม',

    -- Withdraw Summary
    total_withdraw_count INT DEFAULT 0 COMMENT 'จำนวนรายการถอน',
    total_withdraw_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดรวมถอน',
    total_withdraw_mdr DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ค่าธรรมเนียมถอนรวม',

    -- Balance Summary
    opening_deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดฝากยกมา',
    closing_deposit_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดฝากยกไป',
    opening_withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดถอนยกมา',
    closing_withdraw_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดถอนยกไป',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_merchant_date (merchant_id, report_date),
    INDEX idx_report_date (report_date)
) COMMENT 'ตารางรายงานรายวัน';

-- 15. ตาราง withdraw_channels - ช่องทางการถอน
CREATE TABLE withdraw_channels (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    channel_name VARCHAR(100) NOT NULL COMMENT 'ชื่อช่องทาง',
    channel_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'รหัสช่องทาง',
    bank_code VARCHAR(10) COMMENT 'รหัสธนาคาร',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',
    daily_limit DECIMAL(15,2) COMMENT 'วงเงินรายวัน',
    transaction_limit DECIMAL(15,2) COMMENT 'วงเงินต่อรายการ',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข'
) COMMENT 'ตารางช่องทางการถอน';

-- 16. ตาราง system_settings - การตั้งค่าระบบ
CREATE TABLE system_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT 'Key การตั้งค่า',
    setting_value TEXT COMMENT 'ค่าการตั้งค่า',
    setting_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT 'ประเภทข้อมูล',
    description TEXT COMMENT 'รายละเอียด',
    is_public BOOLEAN DEFAULT FALSE COMMENT 'แสดงสาธารณะ',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข'
) COMMENT 'ตารางการตั้งค่าระบบ';

-- notify จะมี 2 แบบ
-- 1. แจ้งเตือน ยอดเงิน อัตโนมัติ หลังมีการเคลือนไหว กลุ่ม private ของลูกค้าเอง
-- 2. ข้อความ Broadcast จากแอดมินไปยัง กลุ่ม support

-- 18. ตาราง telegram_alerts - การตั้งค่าการแจ้งเตือนของร้านค้า
CREATE TABLE telegram_alerts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสร้านค้า',
    notify_type ENUM('DEPOSIT', 'WITHDRAW', 'TOPUP', 'TRANSFER', 'SETTLEMENT') NOT NULL COMMENT 'ประเภทการแจ้งเตือน',

    -- การตั้งค่าการแจ้งเตือน
    message_template TEXT COMMENT 'ข้อความแจ้งเตือนที่กำหนดเอง (ถ้าไม่ใส่จะใช้ template เริ่มต้น)',
    chat_id VARCHAR(50) NOT NULL COMMENT 'Chat ID ของ Telegram',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT 'เปิด/ปิดการแจ้งเตือนประเภทนี้',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_merchant_type (merchant_id, notify_type),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_notify_type (notify_type),
    INDEX idx_is_enabled (is_enabled)
) COMMENT 'ตารางการตั้งค่าการแจ้งเตือนของร้านค้า';

-- 19. ตาราง telegram_channels - กลุ่ม Telegram สำหรับ Broadcast
CREATE TABLE telegram_channels (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'ชื่อกลุ่ม',
    chat_id VARCHAR(50) NOT NULL COMMENT 'Chat ID ของ Telegram',
    description TEXT COMMENT 'รายละเอียดกลุ่ม',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    UNIQUE KEY unique_chat_id (chat_id),
    INDEX idx_is_active (is_active)
) COMMENT 'ตารางกลุ่ม Telegram สำหรับ Broadcast';

-- 20. ตาราง telegram_messages - ข้อความ Broadcast
CREATE TABLE telegram_messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    admin_user_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสผู้ดูแลที่สร้างข้อความ',
    title VARCHAR(200) COMMENT 'หัวข้อข้อความ',
    text TEXT NOT NULL COMMENT 'เนื้อหาข้อความ',
    image_url VARCHAR(500) COMMENT 'URL รูปภาพ (ถ้ามี)',

    -- Status
    status ENUM('DRAFT', 'SCHEDULED', 'SENDING', 'SENT', 'FAILED') DEFAULT 'DRAFT' COMMENT 'สถานะข้อความ',
    scheduled_at TIMESTAMP NULL COMMENT 'กำหนดเวลาส่ง',
    sent_at TIMESTAMP NULL COMMENT 'เวลาที่ส่งจริง',

    -- Statistics
    total_channels INT DEFAULT 0 COMMENT 'จำนวนกลุ่มที่จะส่ง',
    sent_channels INT DEFAULT 0 COMMENT 'จำนวนกลุ่มที่ส่งสำเร็จ',
    failed_channels INT DEFAULT 0 COMMENT 'จำนวนกลุ่มที่ส่งล้มเหลว',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_user_id (admin_user_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_at (scheduled_at)
) COMMENT 'ตารางข้อความ Broadcast';

-- 21. ตาราง telegram_message_channels - ความสัมพันธ์ข้อความ ↔ กลุ่ม
CREATE TABLE telegram_message_channels (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    message_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสข้อความ',
    channel_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสกลุ่ม',

    -- Delivery Status
    status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING' COMMENT 'สถานะการส่ง',
    sent_at TIMESTAMP NULL COMMENT 'เวลาที่ส่งสำเร็จ',
    error_message TEXT COMMENT 'ข้อความข้อผิดพลาด',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (message_id) REFERENCES telegram_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (channel_id) REFERENCES telegram_channels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_message_channel (message_id, channel_id),
    INDEX idx_message_id (message_id),
    INDEX idx_channel_id (channel_id),
    INDEX idx_status (status)
) COMMENT 'ตารางความสัมพันธ์ข้อความ ↔ กลุ่ม';

-- 22. ตาราง audit_logs - บันทึกการตรวจสอบ
CREATE TABLE audit_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED COMMENT 'รหัสผู้ใช้',
    merchant_id BIGINT UNSIGNED COMMENT 'รหัสร้านค้า',
    action VARCHAR(100) NOT NULL COMMENT 'การกระทำ',
    table_name VARCHAR(100) COMMENT 'ชื่อตาราง',
    record_id BIGINT UNSIGNED COMMENT 'รหัสระเบียน',
    old_values JSON COMMENT 'ค่าเดิม',
    new_values JSON COMMENT 'ค่าใหม่',
    ip_address VARCHAR(45) COMMENT 'IP Address',
    user_agent TEXT COMMENT 'User Agent',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) COMMENT 'ตารางบันทึกการตรวจสอบ';
-- =====================================================
-- Request API Merchant เชื่อม​โยง table Transaction
-- =====================================================
-- 23. ตาราง Log RequestDeposit - รายการฝาก + topup
-- 24. ตาราง Log RequestWithdraw - รายการถอน
-- 25. ตาราง Log Requestransfer - รายการโอน
-- 26. ตาราง Log RequestSettlement - รายการ Settlement + auto
-- 26.5 ตาราง Log Callback - รายการ Callback

-- 27. ตาราง bank_transactions - รายการธุรกรรมธนาคาร
-- วัตถุประสงค์: เก็บบันทึกธุรกรรมทั้งหมดที่เกิดขึ้นในบัญชีธนาคาร
-- ใช้สำหรับ: ติดตามการเคลื่อนไหวเงิน, ตรวจสอบยอดคงเหลือ, ป้องกันธุรกรรมซ้ำ
-- ความสัมพันธ์: เชื่อมโยงกับ bank_accounts และ transactions (Double Entry)

-- =====================================================
-- ระบบ Double Entry Bookkeeping (ระบบบัญชีคู่)
-- =====================================================

-- 28. ตาราง accounts - ผังบัญชี
CREATE TABLE accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    account_code VARCHAR(20) NOT NULL UNIQUE COMMENT 'รหัสบัญชี เช่น 1100, 1200',
    account_name VARCHAR(100) NOT NULL COMMENT 'ชื่อบัญชี เช่น เงินสดในธนาคาร',
    account_type ENUM('ASSET', 'LIABILITY', 'REVENUE', 'EXPENSE') NOT NULL COMMENT 'ประเภทบัญชี: สินทรัพย์, หนี้สิน, รายได้, ค่าใช้จ่าย',
    normal_balance ENUM('DEBIT', 'CREDIT') NOT NULL COMMENT 'ยอดปกติ: เดบิต หรือ เครดิต',
    current_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดคงเหลือปัจจุบัน',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'สถานะการใช้งาน',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    INDEX idx_account_code (account_code),
    INDEX idx_account_type (account_type),
    INDEX idx_is_active (is_active)
) COMMENT 'ตารางผังบัญชีสำหรับระบบบัญชีคู่';

-- 29. ตาราง journal_entries - รายการบัญชีหลัก
CREATE TABLE journal_entries (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    journal_ref VARCHAR(100) NOT NULL UNIQUE COMMENT 'เลขที่รายการบัญชี เช่น JE20250622001',
    transaction_id BIGINT UNSIGNED NULL COMMENT 'รหัสธุรกรรมที่เกี่ยวข้อง',
    entry_date DATE NOT NULL COMMENT 'วันที่บันทึกรายการ',
    description TEXT NOT NULL COMMENT 'คำอธิบายรายการ',
    total_amount DECIMAL(15,2) NOT NULL COMMENT 'ยอดเงินรวมของรายการ',
    status ENUM('DRAFT', 'POSTED') DEFAULT 'POSTED' COMMENT 'สถานะ: ร่าง หรือ บันทึกแล้ว',
    created_by BIGINT UNSIGNED NULL COMMENT 'ผู้สร้างรายการ',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'วันที่แก้ไข',

    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_journal_ref (journal_ref),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_entry_date (entry_date),
    INDEX idx_status (status)
) COMMENT 'ตารางรายการบัญชีหลัก - แต่ละรายการต้องมี Debit = Credit';

-- 30. ตาราง journal_lines - รายการย่อยบัญชี (Double Entry Lines)
CREATE TABLE journal_lines (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    journal_entry_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสรายการบัญชีหลัก',
    account_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสบัญชีที่ใช้',
    debit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเดบิต (ด้านซ้าย)',
    credit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเครดิต (ด้านขวา)',
    description TEXT COMMENT 'คำอธิบายรายการย่อย',
    reference_type ENUM('merchant', 'bank_account', 'system', 'user') NOT NULL COMMENT 'ประเภทการอ้างอิง',
    reference_id BIGINT UNSIGNED NULL COMMENT 'รหัสที่อ้างอิง เช่น รหัสร้านค้า',
    line_number INT NOT NULL COMMENT 'เลขที่บรรทัด เรียงลำดับ 1, 2, 3...',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE RESTRICT,
    INDEX idx_journal_entry_id (journal_entry_id),
    INDEX idx_account_id (account_id),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_line_number (journal_entry_id, line_number)
) COMMENT 'ตารางรายการย่อยบัญชี - แต่ละบรรทัดของ Double Entry';

-- 31. ตาราง balance_snapshots - สแนปช็อตยอดคงเหลือรายวัน
CREATE TABLE balance_snapshots (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    snapshot_date DATE NOT NULL COMMENT 'วันที่ทำสแนปช็อต',
    account_id BIGINT UNSIGNED NOT NULL COMMENT 'รหัสบัญชี',
    opening_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดยกมา (จากวันก่อนหน้า)',
    debit_total DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเดบิตรวมในวันนี้',
    credit_total DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดเครดิตรวมในวันนี้',
    closing_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT 'ยอดยกไป (ณ สิ้นวัน)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'วันที่สร้าง',

    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_account_date (account_id, snapshot_date),
    INDEX idx_snapshot_date (snapshot_date),
    INDEX idx_account_id (account_id)
) COMMENT 'ตารางเก็บสแนปช็อตยอดคงเหลือของแต่ละบัญชีรายวัน';

-- เพิ่มข้อมูลผังบัญชีเริ่มต้น
INSERT INTO accounts (account_code, account_name, account_type, normal_balance, current_balance) VALUES
-- บัญชีสินทรัพย์ (Assets) - ยอดปกติด้าน Debit
('1100', 'เงินสดในธนาคาร', 'ASSET', 'DEBIT', 0.00),
('1200', 'ยอดเงินฝากของร้านค้า', 'ASSET', 'DEBIT', 0.00),
('1300', 'ยอดเงินถอนของร้านค้า', 'ASSET', 'DEBIT', 0.00),
('1400', 'ลูกหนี้การค้า', 'ASSET', 'DEBIT', 0.00),
('1500', 'ค่าใช้จ่ายจ่ายล่วงหน้า', 'ASSET', 'DEBIT', 0.00),

-- บัญชีหนี้สิน (Liabilities) - ยอดปกติด้าน Credit
('2100', 'เจ้าหนี้ร้านค้า', 'LIABILITY', 'CREDIT', 0.00),
('2200', 'ค่าใช้จ่ายค้างจ่าย', 'LIABILITY', 'CREDIT', 0.00),
('2300', 'รายได้รับล่วงหน้า', 'LIABILITY', 'CREDIT', 0.00),

-- บัญชีรายได้ (Revenue) - ยอดปกติด้าน Credit
('4100', 'รายได้ค่าธรรมเนียม MDR - ฝาก', 'REVENUE', 'CREDIT', 0.00),
('4200', 'รายได้ค่าธรรมเนียม MDR - ถอน', 'REVENUE', 'CREDIT', 0.00),
('4300', 'รายได้ค่าธรรมเนียม MDR - Settlement', 'REVENUE', 'CREDIT', 0.00),
('4400', 'รายได้บริการอื่นๆ', 'REVENUE', 'CREDIT', 0.00),

-- บัญชีค่าใช้จ่าย (Expenses) - ยอดปกติด้าน Debit
('5100', 'ค่าธรรมเนียมธนาคาร', 'EXPENSE', 'DEBIT', 0.00),
('5200', 'ค่าบำรุงรักษาระบบ', 'EXPENSE', 'DEBIT', 0.00),
('5300', 'ค่าใช้จ่ายในการประมวลผล', 'EXPENSE', 'DEBIT', 0.00),
('5400', 'ค่าใช้จ่ายบริหาร', 'EXPENSE', 'DEBIT', 0.00);

-- 
-- =====================================================
-- ข้อมูลเริ่มต้น (Initial Data)
-- =====================================================

-- เพิ่มสิทธิ์เริ่มต้น
INSERT INTO permissions (permission_name, permission_key, description, module) VALUES
('ดูแดชบอร์ด', 'dashboard.view', 'ดูหน้าแดชบอร์ด', 'dashboard'),
('ดูรายการธุรกรรม', 'transaction.view', 'ดูรายการธุรกรรม', 'transaction'),
('สร้างรายการฝาก', 'transaction.create_deposit', 'สร้างรายการฝากเงิน', 'transaction'),
('สร้างรายการถอน', 'transaction.create_withdraw', 'สร้างรายการถอนเงิน', 'transaction'),
('อนุมัติการถอน', 'withdraw.approve', 'อนุมัติการถอนเงิน', 'withdraw'),
('ดูรายงานธนาคาร', 'bank_statement.view', 'ดูรายงานธนาคาร', 'bank_statement'),
('จัดการ Settlement', 'settlement.manage', 'จัดการการโอนเงิน', 'settlement'),
('เติมเงิน', 'fund.topup', 'เติมเงินเข้าระบบ', 'fund'),
('โอนเงิน', 'fund.transfer', 'โอนเงินระหว่างบัญชี', 'fund'),
('สแกนสลิป', 'slip.scan', 'สแกนสลิปธนาคาร', 'slip'),
('จัดการ Blacklist', 'blacklist.manage', 'จัดการรายชื่อดำ', 'blacklist'),
('ดูรายงาน', 'report.view', 'ดูรายงานต่างๆ', 'report'),
('จัดการโปรไฟล์', 'profile.manage', 'จัดการข้อมูลโปรไฟล์', 'profile'),
('จัดการผู้ใช้', 'user.manage', 'จัดการผู้ใช้งาน', 'user'),
('จัดการกลุ่มผู้ใช้', 'user_group.manage', 'จัดการกลุ่มผู้ใช้', 'user_group'),
('จัดการสิทธิ์', 'permission.manage', 'จัดการสิทธิ์การใช้งาน', 'permission'),
('ส่ง Broadcast', 'broadcast.send', 'ส่งข้อความ Broadcast ผ่าน Telegram', 'broadcast'),
('จัดการกลุ่ม Telegram', 'telegram_channel.manage', 'จัดการกลุ่ม Telegram สำหรับ Broadcast', 'broadcast'),
('ดูรายงาน Broadcast', 'broadcast.report', 'ดูรายงานการส่ง Broadcast', 'broadcast');

-- เพิ่มกลุ่มผู้ใช้เริ่มต้น
INSERT INTO user_groups (group_name, description) VALUES
('Admin', 'ผู้ดูแลระบบ - มีสิทธิ์เต็ม'),
('Merchant Owner', 'เจ้าของร้านค้า - มีสิทธิ์จัดการร้านค้า'),
('Merchant Staff', 'พนักงานร้านค้า - สิทธิ์จำกัด'),
('Viewer', 'ผู้ดู - สิทธิ์ดูข้อมูลเท่านั้น');

-- เพิ่มการตั้งค่าระบบเริ่มต้น
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('deposit_operating_hours', '{"start": "02:01", "end": "23:00"}', 'JSON', 'ช่วงเวลาทำการของระบบฝาก', TRUE),
('withdraw_operating_hours', '{"start": "00:31", "end": "22:50"}', 'JSON', 'ช่วงเวลาทำการของระบบถอน', TRUE),
('default_deposit_mdr_rate', '1.50', 'NUMBER', 'อัตรา MDR ฝากเงินเริ่มต้น (%)', FALSE),
('default_withdraw_mdr_fixed', '10.00', 'NUMBER', 'ค่าธรรมเนียมถอนเงินคงที่เริ่มต้น (THB)', FALSE),
('default_settlement_mdr_fixed', '10.00', 'NUMBER', 'ค่าธรรมเนียม Settlement คงที่เริ่มต้น (THB)', FALSE),
('max_withdraw_amount', '500000.00', 'NUMBER', 'จำนวนเงินถอนสูงสุดต่อรายการ (THB)', FALSE),
('system_maintenance', 'false', 'BOOLEAN', 'สถานะปิดปรับปรุงระบบ', TRUE),
('telegram_notification_enabled', 'true', 'BOOLEAN', 'เปิดใช้งานการแจ้งเตือน Telegram', FALSE),
('default_telegram_bot_token', '', 'STRING', 'Telegram Bot Token เริ่มต้น', FALSE),
('default_telegram_chat_id', '', 'STRING', 'Telegram Chat ID เริ่มต้น', FALSE);
