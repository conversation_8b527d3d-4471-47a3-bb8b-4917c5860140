# เอกสารระบบ SugarPay Payment System

โฟลเดอร์นี้รวบรวมเอกสารและไฟล์ฐานข้อมูลทั้งหมดของระบบ SugarPay Payment System

---

## 📁 โครงสร้างไฟล์

### 🗄️ ไฟล์ฐานข้อมูล (Database Files)

| ไฟล์ | คำอธิบาย |
|------|----------|
| [`database_schema.sql`](./database_schema.sql) | โครงสร้างฐานข้อมูลครบถ้วน 17 ตาราง พร้อม indexes และ constraints |
| [`sample_data.sql`](./sample_data.sql) | ข้อมูลตัวอย่างสำหรับทดสอบระบบ รวมถึง users, merchants, transactions |
| [`stored_procedures.sql`](./stored_procedures.sql) | Stored Procedures สำหรับการจัดการธุรกรรมและ business logic |

### 📚 เอกสารระบบ (System Documentation)

| ไฟล์ | คำอธิบาย |
|------|----------|
| [`README_SYSTEM_DOCUMENTATION.md`](./README_SYSTEM_DOCUMENTATION.md) | เอกสารอธิบายระบบโดยรวม ฟีเจอร์ และการทำงาน |
| [`DATABASE_SCHEMA_DOCUMENTATION.md`](./DATABASE_SCHEMA_DOCUMENTATION.md) | เอกสาร Database Schema รายละเอียด พร้อมความสัมพันธ์ระหว่างตาราง |
| [`PROJECT_SUMMARY.md`](./PROJECT_SUMMARY.md) | สรุปโปรเจคทั้งหมด ฟีเจอร์หลัก และการใช้งาน |

### 🔧 เอกสารเทคนิค (Technical Documentation)

| ไฟล์ | คำอธิบาย |
|------|----------|
| [`API_INTEGRATION_GUIDE.md`](./API_INTEGRATION_GUIDE.md) | คู่มือการเชื่อมต่อ API พร้อมตัวอย่าง code และ SDK |
| [`BANK_ACCOUNTS_MANY_TO_MANY.md`](./BANK_ACCOUNTS_MANY_TO_MANY.md) | อธิบายการออกแบบ Bank Accounts แบบ Many-to-Many |
| [`EXAMPLE_ADD_BANK_ACCOUNTS.md`](./EXAMPLE_ADD_BANK_ACCOUNTS.md) | ตัวอย่างการเพิ่มบัญชีธนาคารให้ร้านค้า พร้อม SQL queries |
| [`TELEGRAM_NOTIFICATION_SYSTEM.md`](./TELEGRAM_NOTIFICATION_SYSTEM.md) | ระบบการแจ้งเตือนผ่าน Telegram สำหรับ 5 ประเภทธุรกรรม |
| [`TELEGRAM_BROADCAST_SYSTEM.md`](./TELEGRAM_BROADCAST_SYSTEM.md) | ระบบ Broadcast ข้อความสำหรับ Admin ไปยังกลุ่ม Telegram |


---

## 🚀 การเริ่มต้นใช้งาน

### 1. ติดตั้งฐานข้อมูล

```bash
# สร้างฐานข้อมูล
mysql -u root -p -e "CREATE DATABASE sugarpay_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# นำเข้าโครงสร้างฐานข้อมูล
mysql -u root -p sugarpay_db < database_schema.sql

# นำเข้าข้อมูลตัวอย่าง
mysql -u root -p sugarpay_db < sample_data.sql

# นำเข้า Stored Procedures
mysql -u root -p sugarpay_db < stored_procedures.sql
```

### 2. ตรวจสอบการติดตั้ง

```sql
-- ตรวจสอบตารางทั้งหมด
SHOW TABLES;

-- ตรวจสอบข้อมูลตัวอย่าง
SELECT COUNT(*) as total_merchants FROM merchants;
SELECT COUNT(*) as total_transactions FROM transactions;
SELECT COUNT(*) as total_bank_accounts FROM bank_accounts;
```

### 3. ทดสอบ API

อ่านคู่มือใน [`API_INTEGRATION_GUIDE.md`](./API_INTEGRATION_GUIDE.md) สำหรับการทดสอบ API endpoints

---

## 📋 ฟีเจอร์หลักของระบบ

### 💰 ระบบจัดการยอดเงิน
- **Deposit Balance** - ยอดเงินฝาก (หัก MDR 1.5%)
- **Withdraw Balance** - ยอดเงินถอน (ค่าธรรมเนียม 10 THB)
- **Frozen Balance** - ยอดเงินที่ถูกระงับ

### 🏦 ระบบบัญชีธนาคาร (Many-to-Many)
- **DEPOSIT** - บัญชีรับเงิน (สูงสุด 10 บัญชี/ร้าน)
- **WITHDRAW** - บัญชีจ่ายเงิน (สูงสุด 10 บัญชี/ร้าน)
- **SAVINGS** - บัญชีออมทรัพย์ (สูงสุด 10 บัญชี/ร้าน)

### 📱 ระบบ Scan Slip
- Import Slip - อัปโหลดรูปสลิป
- Verify Slip - ตรวจสอบและจับคู่รายการ
- OCR อ่านข้อมูลอัตโนมัติ

### 🔐 ระบบความปลอดภัย
- API Key & Secret Key Authentication
- Google 2FA Support
- PIN Code สำหรับการถอนเงิน
- IP Whitelist
- Audit Trail ครบถ้วน

### 📱 ระบบการแจ้งเตือน Telegram (แบบ Merchant-Based)
- **5 ประเภทการแจ้งเตือน**: Deposit, Withdraw, TopUp, Transfer, Settlement
- **ตั้งค่าแยกร้านค้า**: แต่ละร้านค้าเปิด/ปิดการแจ้งเตือนได้อิสระ
- **ข้อความกำหนดเอง**: ร้านค้าสามารถใช้ข้อความของตัวเองได้
- **กรองจำนวนเงิน**: แจ้งเตือนเฉพาะจำนวนเงินที่กำหนด
- **หน่วงเวลา**: ส่งการแจ้งเตือนหลังจากเวลาที่กำหนด

### 📢 ระบบ Telegram Broadcast (สำหรับ Admin)
- **ส่งข้อความหลายกลุ่ม**: Broadcast ไปหลายกลุ่มพร้อมกัน
- **กำหนดเวลาส่ง**: Schedule ข้อความล่วงหน้า
- **รองรับรูปภาพ**: ส่งข้อความพร้อมรูปภาพ
- **ติดตามสถานะ**: ดูสถิติการส่งแบบ Real-time
- **จัดการกลุ่ม**: เพิ่ม/ลบ/เปิด-ปิดกลุ่มได้



---

## 💳 ค่าธรรมเนียม MDR

| ประเภทรายการ | ค่าธรรมเนียม | หมายเหตุ |
|-------------|-------------|----------|
| **Deposit** | 1.5% | หักจากยอดฝาก |
| **Withdraw** | 10 THB/รายการ | เรียกเก็บแยกต่างหาก |
| **TopUp** | 1.5% | เติมเงินเข้าระบบ |
| **Transfer** | ฟรี | โอนระหว่างบัญชี |
| **Settlement** | 10 THB/รายการ | โอนอัตโนมัติ |

---

## ⏰ ช่วงเวลาทำการ

- **ระบบฝาก**: 02:01 - 23:00 น. (ปกติ), 23:01 - 02:00 น. (ล่าช้า)
- **ระบบถอน**: 00:31 - 22:50 น. (ปิด 22:51 - 00:30 น.)

---

## 👥 การจัดการผู้ใช้และสิทธิ์

### ประเภทผู้ใช้
- **ADMIN** - ผู้ดูแลระบบ
- **MERCHANT** - เจ้าของร้านค้า
- **SUB_USER** - ผู้ใช้ย่อย

### กลุ่มสิทธิ์เริ่มต้น
- **Admin** - สิทธิ์เต็ม
- **Merchant Owner** - จัดการร้านค้า
- **Merchant Staff** - สิทธิ์จำกัด
- **Viewer** - ดูข้อมูลเท่านั้น

---

## 📊 ตัวอย่างการใช้งาน

### เพิ่มบัญชีธนาคารใหม่
```sql
-- ดูตัวอย่างใน EXAMPLE_ADD_BANK_ACCOUNTS.md
INSERT INTO bank_accounts (bank_code, bank_name, account_no, account_name) VALUES
('014', 'SCB', '**********', 'บริษัท ABC จำกัด');
```

### สร้างรายการฝากเงิน
```sql
-- ใช้ Stored Procedure
CALL CreateDepositTransaction(1, 'ORDER001', 1000.00, '**********', 'ลูกค้า A', '014', 'https://callback.url', @txn_id, @result_code, @message);
```

---

## 🔧 การพัฒนาต่อยอด

### Database Migration
- ใช้ไฟล์ `database_schema.sql` เป็นฐาน
- เพิ่ม migration scripts สำหรับการอัปเดต
- ทดสอบกับ `sample_data.sql`

### API Development
- ใช้ `stored_procedures.sql` สำหรับ business logic
- ตรวจสอบ API spec ใน `API_INTEGRATION_GUIDE.md`
- ทดสอบกับ Sandbox environment

### Security Implementation
- ใช้ API Key/Secret authentication
- ตรวจสอบ IP Whitelist
- บันทึก Audit Logs ทุกการกระทำ

---

## 📞 การสนับสนุน

- **Email**: <EMAIL>
- **Phone**: 02-123-4567
- **Documentation**: อ่านเอกสารในโฟลเดอร์นี้
- **Issues**: สร้าง issue ใน repository

---

## 📝 หมายเหตุ

- เอกสารทั้งหมดเขียนเป็นภาษาไทย
- ใช้ `created_at` naming convention สำหรับ timestamp fields
- รองรับ Double Entry Accounting แบบ Basic
- ออกแบบให้รองรับการขยายตัวในอนาคต

**อัปเดตล่าสุด**: 21 มิถุนายน 2025
