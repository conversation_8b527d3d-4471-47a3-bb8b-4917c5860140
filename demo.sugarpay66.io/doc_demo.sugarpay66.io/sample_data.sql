-- =====================================================
-- Sample Data สำหรับระบบ SugarPay
-- =====================================================

-- เพิ่มผู้ใช้ตัวอย่าง
INSERT INTO users (username, email, password, first_name, last_name, phone, user_type, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'System', '0800000000', 'ADMIN', 'ACTIVE'),
('tiger-001', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Tiger', 'Shop', '**********', 'MERCHANT', 'ACTIVE'),
('lion-002', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lion', 'Store', '**********', 'MERCHANT', 'ACTIVE'),
('staff001', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Staff', 'One', '**********', 'SUB_USER', 'ACTIVE');

-- เพิ่มข้อมูลร้านค้า
INSERT INTO merchants (user_id, merchant_code, merchant_name, business_type, status, api_key, secret_key, callback_url, deposit_mdr_rate, withdraw_mdr_fixed, bank_name, bank_account_no, bank_account_name) VALUES
(2, 'TIGER001', 'Tiger Shop', 'E-commerce', 'ACTIVE', 'd79edc5c-e89667cb-1210320f-b0059db0', 'c7b5f857-f418c9c6-c9f7e4b7-83c691ae', 'https://tiger-shop.com/callback', 1.50, 10.00, 'SCB', '**********', 'Tiger Shop Co., Ltd.'),
(3, 'LION002', 'Lion Store', 'Retail', 'ACTIVE', 'a1b2c3d4-e5f6g7h8-i9j0k1l2-m3n4o5p6', 'q7r8s9t0-u1v2w3x4-y5z6a7b8-c9d0e1f2', 'https://lion-store.com/callback', 1.50, 10.00, 'KBANK', '**********', 'Lion Store Ltd.');

-- เพิ่มยอดเงินเริ่มต้น
INSERT INTO merchant_balances (merchant_id, deposit_balance, withdraw_balance, frozen_balance) VALUES
(1, 50000.00, 25000.00, 0.00),
(2, 75000.00, 30000.00, 5000.00);

-- เพิ่มบัญชีธนาคารตัวอย่าง (Master)
INSERT INTO bank_accounts (bank_code, bank_name, account_no, account_name, current_balance, balance, is_active, daily_limit) VALUES
('014', 'SCB', '**********', 'บริษัท วิวัฒน์ อินดีไซน์ จำกัด', 100000.00, 100000.00, TRUE, 1000000.00),
('004', 'KBANK', '**********', 'บริษัท ซูเปอร์ทรีบิ้วตี้สโตร์ จำกัด', 50000.00, 50000.00, TRUE, 500000.00),
('014', 'SCB', '**********', 'บจก.วุฒิกรช็อป 1998 (Pool)', 200000.00, 200000.00, TRUE, 2000000.00),
('025', 'BAY', '**********', 'Tiger Shop Withdraw Account', 150000.00, 150000.00, TRUE, 1500000.00),
('030', 'GSB', '**********', 'Tiger Shop Savings', 500000.00, 500000.00, TRUE, NULL),
('004', 'KBANK', '**********', 'Lion Store Deposit', 80000.00, 80000.00, TRUE, 800000.00),
('004', 'KBANK', '**********', 'Lion Store Withdraw', 120000.00, 120000.00, TRUE, 1200000.00),
('002', 'BBL', '**********', 'Lion Store Savings', 300000.00, 300000.00, TRUE, NULL),
('014', 'SCB', '**********', 'Shared SCB Account', 300000.00, 300000.00, TRUE, 2000000.00),
('004', 'KBANK', '**********', 'Shared KBANK Account', 250000.00, 250000.00, TRUE, 1500000.00);

-- เพิ่มความสัมพันธ์ระหว่างร้านค้าและบัญชีธนาคาร
INSERT INTO merchant_bank_accounts (merchant_id, bank_account_id, account_type, is_primary, is_active, merchant_daily_limit, priority_order) VALUES
-- Tiger Shop (merchant_id = 1)
-- Deposit Accounts
(1, 1, 'DEPOSIT', TRUE, TRUE, 1000000.00, 1),   -- SCB Primary
(1, 2, 'DEPOSIT', FALSE, TRUE, 500000.00, 2),   -- KBANK Secondary
(1, 9, 'DEPOSIT', FALSE, TRUE, 800000.00, 3),   -- Shared SCB

-- Withdraw Accounts
(1, 3, 'WITHDRAW', TRUE, TRUE, 2000000.00, 1),  -- SCB Primary
(1, 4, 'WITHDRAW', FALSE, TRUE, 1500000.00, 2), -- BAY Secondary

-- Savings Accounts
(1, 5, 'SAVINGS', TRUE, TRUE, NULL, 1),         -- GSB Primary

-- Lion Store (merchant_id = 2)
-- Deposit Accounts
(2, 6, 'DEPOSIT', TRUE, TRUE, 800000.00, 1),    -- KBANK Primary
(2, 10, 'DEPOSIT', FALSE, TRUE, 600000.00, 2),  -- Shared KBANK

-- Withdraw Accounts
(2, 7, 'WITHDRAW', TRUE, TRUE, 1200000.00, 1),  -- KBANK Primary

-- Savings Accounts
(2, 8, 'SAVINGS', TRUE, TRUE, NULL, 1),         -- BBL Primary

-- ตัวอย่างการใช้บัญชีร่วมกัน - บัญชี SCB ใช้ได้กับทั้ง 2 ร้าน
(2, 9, 'WITHDRAW', FALSE, TRUE, 500000.00, 2);  -- Lion Store ใช้ Shared SCB เป็น Withdraw รอง

-- เพิ่มรายการธุรกรรมตัวอย่าง
INSERT INTO transactions (merchant_id, order_id, transaction_type, amount, mdr_fee, net_amount, status, merchant_bank_account_id, customer_bank_account_no, customer_bank_account_name, customer_bank_code, channel, transaction_date) VALUES
-- Tiger Shop Transactions
(1, 'TXN001', 'DEPOSIT', 1000.00, 15.00, 985.00, 'SUCCESS', 1, '**********', 'ลูกค้า A', '014', 'API', '2025-05-03 10:30:00'),
(1, 'TXN002', 'DEPOSIT', 2500.00, 37.50, 2462.50, 'SUCCESS', 2, '**********', 'ลูกค้า B', '004', 'API', '2025-05-03 11:15:00'),
(1, 'TXN003', 'WITHDRAW', 5000.00, 10.00, 4990.00, 'SUCCESS', 4, '**********', 'ลูกค้า C', '025', 'API', '2025-05-03 14:20:00'),
(1, 'TXN004', 'TOPUP', 10000.00, 150.00, 9850.00, 'SUCCESS', 1, NULL, NULL, NULL, 'WEB', '2025-05-03 09:00:00'),
(1, 'TXN005', 'TRANSFER', 3000.00, 0.00, 3000.00, 'SUCCESS', NULL, NULL, NULL, NULL, 'WEB', '2025-05-03 16:45:00'),

-- Lion Store Transactions
(2, 'LION001', 'DEPOSIT', 1500.00, 22.50, 1477.50, 'SUCCESS', 7, '**********', 'ลูกค้า D', '002', 'API', '2025-05-03 12:00:00'),
(2, 'LION002', 'WITHDRAW', 3000.00, 10.00, 2990.00, 'SUCCESS', 8, '**********', 'ลูกค้า E', '004', 'API', '2025-05-03 15:30:00'),

-- Pending Transactions
(1, 'TXN006', 'DEPOSIT', 800.00, 12.00, 788.00, 'WAIT_CONFIRM', 3, '**********', 'ลูกค้า F', '014', 'API', '2025-05-03 17:00:00'),
(2, 'LION003', 'WITHDRAW', 2000.00, 10.00, 1990.00, 'CREATE', 8, '**********', 'ลูกค้า G', '004', 'API', '2025-05-03 17:30:00');





-- เพิ่มงาน Scan Slip ตัวอย่าง
INSERT INTO scan_slip_tasks (merchant_id, batch_id, slip_image_url, bank_code, account_no, amount, transaction_date, reference_no, status, matched_transaction_id) VALUES
(1, 'BATCH001', '/uploads/slips/slip001.jpg', '014', '**********', 1000.00, '2025-05-03 10:30:00', 'REF001', 'MATCHED', 1),
(1, 'BATCH001', '/uploads/slips/slip002.jpg', '014', '**********', 2500.00, '2025-05-03 11:15:00', 'REF002', 'MATCHED', 2),
(1, 'BATCH002', '/uploads/slips/slip003.jpg', '014', '**********', 800.00, '2025-05-03 17:00:00', 'REF003', 'VERIFIED', NULL),
(2, 'BATCH003', '/uploads/slips/slip004.jpg', '004', '**********', 1500.00, '2025-05-03 12:00:00', 'REF004', 'MATCHED', 6);

-- เพิ่มรายชื่อดำตัวอย่าง
INSERT INTO blacklist (merchant_id, blacklist_type, blacklist_value, reason, is_active) VALUES
(1, 'BANK_ACCOUNT', '**********', 'บัญชีปลอม', TRUE),
(1, 'PHONE', '**********', 'หมายเลขโทรศัพท์ต้องสงสัย', TRUE),
(1, 'EMAIL', '<EMAIL>', 'อีเมลสแปม', TRUE),
(2, 'IP', '************0', 'IP ต้องสงสัย', TRUE);

-- เพิ่มรายงานรายวันตัวอย่าง
INSERT INTO daily_reports (merchant_id, report_date, total_deposit_count, total_deposit_amount, total_deposit_mdr, total_withdraw_count, total_withdraw_amount, total_withdraw_mdr, opening_deposit_balance, closing_deposit_balance, opening_withdraw_balance, closing_withdraw_balance) VALUES
(1, '2025-05-02', 5, 15000.00, 225.00, 3, 12000.00, 30.00, 45000.00, 48225.00, 30000.00, 18970.00),
(1, '2025-05-03', 3, 4300.00, 64.50, 1, 5000.00, 10.00, 48225.00, 52489.50, 18970.00, 13960.00),
(2, '2025-05-02', 2, 8000.00, 120.00, 2, 6000.00, 20.00, 70000.00, 77880.00, 25000.00, 18980.00),
(2, '2025-05-03', 1, 1500.00, 22.50, 1, 3000.00, 10.00, 77880.00, 79357.50, 18980.00, 15970.00);

-- เพิ่มช่องทางการถอนตัวอย่าง
INSERT INTO withdraw_channels (channel_name, channel_code, bank_code, is_active, daily_limit, transaction_limit) VALUES
('SCB PromptPay', 'SCB_PROMPTPAY', '014', TRUE, 2000000.00, 50000.00),
('KBANK Transfer', 'KBANK_TRANSFER', '004', TRUE, 1500000.00, 100000.00),
('BAY Mobile Banking', 'BAY_MOBILE', '025', TRUE, 1000000.00, 30000.00),
('BBL Internet Banking', 'BBL_INTERNET', '002', TRUE, 3000000.00, 200000.00);

-- เพิ่มการตั้งค่าระบบเพิ่มเติม
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('company_name', 'SugarPay66 Co., Ltd.', 'STRING', 'ชื่อบริษัท', TRUE),
('support_email', '<EMAIL>', 'STRING', 'อีเมลสนับสนุน', TRUE),
('support_phone', '02-123-4567', 'STRING', 'เบอร์โทรสนับสนุน', TRUE),
('min_deposit_amount', '10.00', 'NUMBER', 'จำนวนเงินฝากขั้นต่ำ (THB)', FALSE),
('min_withdraw_amount', '100.00', 'NUMBER', 'จำนวนเงินถอนขั้นต่ำ (THB)', FALSE),
('transaction_timeout_minutes', '30', 'NUMBER', 'เวลาหมดอายุรายการ (นาที)', FALSE),
('auto_settlement_enabled', 'false', 'BOOLEAN', 'เปิดใช้งาน Auto Settlement', FALSE),
('auto_settlement_time', '23:30', 'STRING', 'เวลา Auto Settlement', FALSE);

-- เพิ่มตัวอย่างการแจ้งเตือนแบบ Basic
-- (ข้อมูลเริ่มต้นถูกเพิ่มแล้วใน database_schema.sql)

-- เพิ่มการตั้งค่าการแจ้งเตือนเพิ่มเติมสำหรับร้านค้าอื่นๆ
-- ตัวอย่าง: ร้านค้าใหม่ที่ต้องการการแจ้งเตือนแบบกำหนดเอง
INSERT INTO merchant_notify_settings (merchant_id, notify_type, is_enabled, notify_to, min_amount, max_amount, custom_message_template, delay_minutes) VALUES
-- การตั้งค่าสำหรับ Tiger Shop (เพิ่มเติม)
(1, 'DEPOSIT', TRUE, '-100**********,-100**********', 50.00, 100000.00, '🐅 Tiger Shop: รับเงินฝาก {{amount}} บาท จาก {{user}} ✅', 0),

-- การตั้งค่าสำหรับ Lion Store (เพิ่มเติม)
(2, 'WITHDRAW', TRUE, '-1002345678901', 500.00, 50000.00, '🦁 Lion Store: จ่ายเงินถอน {{amount}} บาท ให้ {{user}} 💸', 2);

-- ตัวอย่างการปรับแต่งการแจ้งเตือนของร้านค้า
UPDATE merchant_notify_settings
SET custom_message_template = '🐅 Tiger Shop: เติมเงิน {{amount}} บาท เข้าบัญชีถอนเรียบร้อย 💰',
    delay_minutes = 1
WHERE merchant_id = 1 AND notify_type = 'TOPUP';

-- เพิ่มตัวอย่างข้อความ Broadcast
INSERT INTO telegram_messages (admin_user_id, title, text, status, total_channels, sent_channels, failed_channels, sent_at) VALUES
(1, 'ประกาศปิดปรับปรุงระบบ',
 '📢 แจ้งปิดปรับปรุงระบบ\n\n🔧 วันที่: 25 มิถุนายน 2025\n⏰ เวลา: 02:00 - 04:00 น.\n\n💡 ระบบจะไม่สามารถใช้งานได้ชั่วคราว\nขออภัยในความไม่สะดวก 🙏',
 'SENT', 3, 3, 0, '2025-06-20 15:30:00'),

(1, 'อัปเดตค่าธรรมเนียมใหม่',
 '💰 อัปเดตค่าธรรมเนียม\n\n📈 มีผลตั้งแต่ 1 กรกฎาคม 2025:\n• ฝากเงิน: 1.5% (เดิม)\n• ถอนเงิน: 12 บาท/รายการ (เพิ่มขึ้นจาก 10 บาท)\n• Settlement: 12 บาท/รายการ (เพิ่มขึ้นจาก 10 บาท)\n\nสอบถามเพิ่มเติม: <EMAIL>',
 'SCHEDULED', 2, 0, 0, NULL),

(1, 'ฟีเจอร์ใหม่: Scan Slip',
 '🆕 ฟีเจอร์ใหม่!\n\n📱 Scan Slip อัตโนมัติ\n✅ อัปโหลดสลิปธนาคาร\n✅ ระบบอ่านข้อมูลอัตโนมัติ\n✅ จับคู่รายการทันที\n\n🚀 เริ่มใช้งานได้แล้ววันนี้!',
 'DRAFT', 0, 0, 0, NULL);

-- เพิ่มความสัมพันธ์ข้อความ ↔ กลุ่ม
INSERT INTO telegram_message_channels (message_id, channel_id, status, sent_at) VALUES
-- ข้อความปิดปรับปรุงระบบ (ส่งไปทุกกลุ่มยกเว้นกลุ่มทดสอบ)
(1, 1, 'SENT', '2025-06-20 15:30:15'), -- กลุ่มแอดมิน
(1, 2, 'SENT', '2025-06-20 15:30:18'), -- กลุ่มร้านค้า
(1, 3, 'SENT', '2025-06-20 15:30:21'), -- กลุ่มประกาศ

-- ข้อความอัปเดตค่าธรรมเนียม (กำหนดส่งไปกลุ่มแอดมินและร้านค้า)
(2, 1, 'PENDING', NULL), -- กลุ่มแอดมิน
(2, 2, 'PENDING', NULL); -- กลุ่มร้านค้า

-- เพิ่ม Audit Logs ตัวอย่าง
INSERT INTO audit_logs (user_id, merchant_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) VALUES
(2, 1, 'CREATE_TRANSACTION', 'transactions', 1, NULL, '{"order_id": "TXN001", "amount": 1000.00, "status": "CREATE"}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, 1, 'UPDATE_TRANSACTION', 'transactions', 1, '{"status": "CREATE"}', '{"status": "SUCCESS"}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, NULL, 'LOGIN', 'users', 1, NULL, '{"login_time": "2025-05-03 08:00:00"}', '********', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
(2, 1, 'UPDATE_BALANCE', 'merchant_balances', 1, '{"deposit_balance": 49000.00}', '{"deposit_balance": 50000.00}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, 1, 'UPDATE_MERCHANT_NOTIFY', 'merchant_notify_settings', 1, '{"is_enabled": true}', '{"is_enabled": false}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, 1, 'UPDATE_MERCHANT_NOTIFY', 'merchant_notify_settings', 2, '{"min_amount": 500.00}', '{"min_amount": 1000.00}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(2, 2, 'UPDATE_MERCHANT_NOTIFY', 'merchant_notify_settings', 6, '{"custom_message_template": null}', '{"custom_message_template": "🦁 Lion Store: ฝากเงิน {{amount}} บาท จาก {{user}}"}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, NULL, 'CREATE_BROADCAST', 'telegram_messages', 1, NULL, '{"title": "ประกาศปิดปรับปรุงระบบ", "status": "DRAFT"}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, NULL, 'SEND_BROADCAST', 'telegram_messages', 1, '{"status": "DRAFT"}', '{"status": "SENT", "sent_channels": 3}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
(1, NULL, 'CREATE_TELEGRAM_CHANNEL', 'telegram_channels', 1, NULL, '{"name": "กลุ่มแอดมิน", "chat_id": "-100**********"}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
