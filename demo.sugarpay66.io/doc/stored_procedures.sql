-- =====================================================
-- Stored Procedures สำหรับระบบ SugarPay
-- =====================================================

DELIMITER //

-- 1. Procedure สำหรับสร้างรายการฝากเงิน
CREATE PROCEDURE CreateDepositTransaction(
    IN p_merchant_id BIGINT UNSIGNED,
    IN p_order_id VARCHAR(100),
    IN p_amount DECIMAL(15,2),
    IN p_customer_bank_account_no VARCHAR(20),
    IN p_customer_bank_account_name VARCHAR(200),
    IN p_customer_bank_code VARCHAR(10),
    IN p_callback_url VARCHAR(500),
    OUT p_transaction_id BIGINT UNSIGNED,
    OUT p_result_code INT,
    OUT p_result_message VARCHAR(255)
)
BEGIN
    DECLARE v_mdr_rate DECIMAL(5,2);
    DECLARE v_mdr_fee DECIMAL(15,2);
    DECLARE v_net_amount DECIMAL(15,2);
    DECLARE v_bank_account_id BIGINT UNSIGNED;
    DECLARE v_existing_count INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result_code = -1;
        SET p_result_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- ตรวจสอบ Order ID ซ้ำ
    SELECT COUNT(*) INTO v_existing_count 
    FROM transactions 
    WHERE merchant_id = p_merchant_id AND order_id = p_order_id;
    
    IF v_existing_count > 0 THEN
        SET p_result_code = 1001;
        SET p_result_message = 'Order ID already exists';
        ROLLBACK;
    ELSE
        -- ดึงอัตรา MDR
        SELECT deposit_mdr_rate INTO v_mdr_rate 
        FROM merchants 
        WHERE id = p_merchant_id;
        
        -- คำนวณค่าธรรมเนียม
        SET v_mdr_fee = p_amount * v_mdr_rate / 100;
        SET v_net_amount = p_amount - v_mdr_fee;
        
        -- หาบัญชีธนาคารหลักสำหรับฝาก
        SELECT id INTO v_bank_account_id 
        FROM bank_accounts 
        WHERE merchant_id = p_merchant_id 
        AND account_type = 'DEPOSIT' 
        AND is_primary = TRUE 
        AND is_active = TRUE
        LIMIT 1;
        
        -- สร้างรายการธุรกรรม
        INSERT INTO transactions (
            merchant_id, order_id, transaction_type, amount, mdr_fee, net_amount,
            status, bank_account_id, customer_bank_account_no, customer_bank_account_name,
            customer_bank_code, channel, callback_url, created_at
        ) VALUES (
            p_merchant_id, p_order_id, 'DEPOSIT', p_amount, v_mdr_fee, v_net_amount,
            'CREATE', v_bank_account_id, p_customer_bank_account_no, p_customer_bank_account_name,
            p_customer_bank_code, 'API', p_callback_url, NOW()
        );
        
        SET p_transaction_id = LAST_INSERT_ID();
        SET p_result_code = 0;
        SET p_result_message = 'Transaction created successfully';
        
        COMMIT;
    END IF;
END //

-- 2. Procedure สำหรับอัปเดตสถานะธุรกรรมเป็น SUCCESS
CREATE PROCEDURE CompleteDepositTransaction(
    IN p_transaction_id BIGINT UNSIGNED,
    IN p_bank_ref_no VARCHAR(100),
    OUT p_result_code INT,
    OUT p_result_message VARCHAR(255)
)
BEGIN
    DECLARE v_merchant_id BIGINT UNSIGNED;
    DECLARE v_net_amount DECIMAL(15,2);
    DECLARE v_current_status VARCHAR(20);
    DECLARE v_bank_account_no VARCHAR(20);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result_code = -1;
        SET p_result_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- ดึงข้อมูลธุรกรรม
    SELECT t.merchant_id, t.net_amount, t.status, ba.account_no
    INTO v_merchant_id, v_net_amount, v_current_status, v_bank_account_no
    FROM transactions t
    LEFT JOIN bank_accounts ba ON t.bank_account_id = ba.id
    WHERE t.id = p_transaction_id;
    
    -- ตรวจสอบสถานะปัจจุบัน
    IF v_current_status != 'CREATE' AND v_current_status != 'ON_PROCESS' THEN
        SET p_result_code = 2001;
        SET p_result_message = 'Invalid transaction status for completion';
        ROLLBACK;
    ELSE
        -- อัปเดตสถานะธุรกรรม
        UPDATE transactions 
        SET status = 'SUCCESS', 
            bank_ref_no = p_bank_ref_no,
            transaction_date = NOW(),
            updated_at = NOW()
        WHERE id = p_transaction_id;
        
        -- อัปเดตยอดเงินร้านค้า
        UPDATE merchant_balances 
        SET deposit_balance = deposit_balance + v_net_amount,
            updated_at = NOW()
        WHERE merchant_id = v_merchant_id;
        
        -- สร้างรายการบัญชี Double Entry
        INSERT INTO log_transactions_entries (transaction_id, merchant_id, bank_account_no, entry_type, amount, description, reference_no) VALUES
        (p_transaction_id, v_merchant_id, v_bank_account_no, 'DEBIT', v_net_amount + (SELECT mdr_fee FROM transactions WHERE id = p_transaction_id), 'รับเงินฝากจากลูกค้า', p_bank_ref_no),
        (p_transaction_id, v_merchant_id, 'MERCHANT_DEPOSIT', 'CREDIT', v_net_amount, 'เครดิตยอดฝากร้านค้า', p_bank_ref_no),
        (p_transaction_id, v_merchant_id, 'MDR_FEE', 'CREDIT', (SELECT mdr_fee FROM transactions WHERE id = p_transaction_id), 'ค่าธรรมเนียม MDR', p_bank_ref_no);
        
        SET p_result_code = 0;
        SET p_result_message = 'Transaction completed successfully';
        
        COMMIT;
    END IF;
END //

-- 3. Procedure สำหรับสร้างรายการถอนเงิน
CREATE PROCEDURE CreateWithdrawTransaction(
    IN p_merchant_id BIGINT UNSIGNED,
    IN p_order_id VARCHAR(100),
    IN p_amount DECIMAL(15,2),
    IN p_customer_bank_account_no VARCHAR(20),
    IN p_customer_bank_account_name VARCHAR(200),
    IN p_customer_bank_code VARCHAR(10),
    IN p_callback_url VARCHAR(500),
    OUT p_transaction_id BIGINT UNSIGNED,
    OUT p_result_code INT,
    OUT p_result_message VARCHAR(255)
)
BEGIN
    DECLARE v_withdraw_mdr_fixed DECIMAL(10,2);
    DECLARE v_net_amount DECIMAL(15,2);
    DECLARE v_current_withdraw_balance DECIMAL(15,2);
    DECLARE v_bank_account_id BIGINT UNSIGNED;
    DECLARE v_existing_count INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result_code = -1;
        SET p_result_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- ตรวจสอบ Order ID ซ้ำ
    SELECT COUNT(*) INTO v_existing_count 
    FROM transactions 
    WHERE merchant_id = p_merchant_id AND order_id = p_order_id;
    
    IF v_existing_count > 0 THEN
        SET p_result_code = 1001;
        SET p_result_message = 'Order ID already exists';
        ROLLBACK;
    ELSE
        -- ดึงค่าธรรมเนียมถอน
        SELECT withdraw_mdr_fixed INTO v_withdraw_mdr_fixed 
        FROM merchants 
        WHERE id = p_merchant_id;
        
        -- ดึงยอดเงินถอนปัจจุบัน
        SELECT withdraw_balance INTO v_current_withdraw_balance 
        FROM merchant_balances 
        WHERE merchant_id = p_merchant_id;
        
        -- ตรวจสอบยอดเงินเพียงพอ (ไม่รวมค่าธรรมเนียม)
        IF v_current_withdraw_balance < p_amount THEN
            SET p_result_code = 3001;
            SET p_result_message = 'Insufficient withdraw balance';
            ROLLBACK;
        ELSE
            SET v_net_amount = p_amount - v_withdraw_mdr_fixed;
            
            -- หาบัญชีธนาคารหลักสำหรับถอน
            SELECT id INTO v_bank_account_id 
            FROM bank_accounts 
            WHERE merchant_id = p_merchant_id 
            AND account_type = 'WITHDRAW' 
            AND is_primary = TRUE 
            AND is_active = TRUE
            LIMIT 1;
            
            -- สร้างรายการธุรกรรม
            INSERT INTO transactions (
                merchant_id, order_id, transaction_type, amount, mdr_fee, net_amount,
                status, bank_account_id, customer_bank_account_no, customer_bank_account_name,
                customer_bank_code, channel, callback_url, created_at
            ) VALUES (
                p_merchant_id, p_order_id, 'WITHDRAW', p_amount, v_withdraw_mdr_fixed, v_net_amount,
                'CREATE', v_bank_account_id, p_customer_bank_account_no, p_customer_bank_account_name,
                p_customer_bank_code, 'API', p_callback_url, NOW()
            );
            
            SET p_transaction_id = LAST_INSERT_ID();
            
            -- หักยอดเงินถอนทันที (Reserve)
            UPDATE merchant_balances 
            SET withdraw_balance = withdraw_balance - p_amount,
                frozen_balance = frozen_balance + p_amount,
                updated_at = NOW()
            WHERE merchant_id = p_merchant_id;
            
            SET p_result_code = 0;
            SET p_result_message = 'Withdraw transaction created successfully';
            
            COMMIT;
        END IF;
    END IF;
END //

-- 4. Procedure สำหรับโอนเงินระหว่าง Deposit Balance และ Withdraw Balance
CREATE PROCEDURE TransferBalance(
    IN p_merchant_id BIGINT UNSIGNED,
    IN p_amount DECIMAL(15,2),
    IN p_transfer_type ENUM('DEPOSIT_TO_WITHDRAW', 'WITHDRAW_TO_DEPOSIT'),
    OUT p_transaction_id BIGINT UNSIGNED,
    OUT p_result_code INT,
    OUT p_result_message VARCHAR(255)
)
BEGIN
    DECLARE v_current_deposit_balance DECIMAL(15,2);
    DECLARE v_current_withdraw_balance DECIMAL(15,2);
    DECLARE v_order_id VARCHAR(100);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result_code = -1;
        SET p_result_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- ดึงยอดเงินปัจจุบัน
    SELECT deposit_balance, withdraw_balance 
    INTO v_current_deposit_balance, v_current_withdraw_balance
    FROM merchant_balances 
    WHERE merchant_id = p_merchant_id;
    
    -- สร้าง Order ID สำหรับการโอน
    SET v_order_id = CONCAT('TRANSFER_', p_merchant_id, '_', UNIX_TIMESTAMP());
    
    IF p_transfer_type = 'DEPOSIT_TO_WITHDRAW' THEN
        -- ตรวจสอบยอดเงินฝากเพียงพอ
        IF v_current_deposit_balance < p_amount THEN
            SET p_result_code = 4001;
            SET p_result_message = 'Insufficient deposit balance';
            ROLLBACK;
        ELSE
            -- อัปเดตยอดเงิน
            UPDATE merchant_balances 
            SET deposit_balance = deposit_balance - p_amount,
                withdraw_balance = withdraw_balance + p_amount,
                updated_at = NOW()
            WHERE merchant_id = p_merchant_id;
        END IF;
    ELSE
        -- ตรวจสอบยอดเงินถอนเพียงพอ
        IF v_current_withdraw_balance < p_amount THEN
            SET p_result_code = 4002;
            SET p_result_message = 'Insufficient withdraw balance';
            ROLLBACK;
        ELSE
            -- อัปเดตยอดเงิน
            UPDATE merchant_balances 
            SET withdraw_balance = withdraw_balance - p_amount,
                deposit_balance = deposit_balance + p_amount,
                updated_at = NOW()
            WHERE merchant_id = p_merchant_id;
        END IF;
    END IF;
    
    IF p_result_code IS NULL THEN
        -- สร้างรายการธุรกรรม
        INSERT INTO transactions (
            merchant_id, order_id, transaction_type, amount, mdr_fee, net_amount,
            status, channel, created_at, transaction_date
        ) VALUES (
            p_merchant_id, v_order_id, 'TRANSFER', p_amount, 0.00, p_amount,
            'SUCCESS', 'WEB', NOW(), NOW()
        );
        
        SET p_transaction_id = LAST_INSERT_ID();
        SET p_result_code = 0;
        SET p_result_message = 'Balance transfer completed successfully';
        
        COMMIT;
    END IF;
END //

-- 5. Function สำหรับคำนวณยอดรวมรายวัน
CREATE FUNCTION GetDailyTransactionSummary(
    p_merchant_id BIGINT UNSIGNED,
    p_date DATE,
    p_transaction_type VARCHAR(20)
) RETURNS JSON
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    DECLARE v_total_amount DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_total_mdr DECIMAL(15,2) DEFAULT 0.00;
    DECLARE v_result JSON;
    
    SELECT 
        COUNT(*),
        COALESCE(SUM(amount), 0),
        COALESCE(SUM(mdr_fee), 0)
    INTO v_count, v_total_amount, v_total_mdr
    FROM transactions 
    WHERE merchant_id = p_merchant_id 
    AND DATE(created_at) = p_date 
    AND transaction_type = p_transaction_type
    AND status = 'SUCCESS';
    
    SET v_result = JSON_OBJECT(
        'count', v_count,
        'total_amount', v_total_amount,
        'total_mdr', v_total_mdr,
        'net_amount', v_total_amount - v_total_mdr
    );
    
    RETURN v_result;
END //

DELIMITER ;
