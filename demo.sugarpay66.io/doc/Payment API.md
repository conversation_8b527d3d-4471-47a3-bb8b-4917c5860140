# Payment API
https://payment-api-doc.readme.io/reference/introduction


## QR Deposit
POST: /v1/deposit/qrcode
```bash

curl --request POST \
     --url https://xxxx.io/v1/deposit/qrcode \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
    {
    "order_id": "007",
    "amount": "100",
    "ref_name": "Customer name",
    "ref_account_no": "**********",
    "ref_bank_code": "014",
    "user_id": "username",
    "callback_url": "https://payment-api-doc.readme.io/reference/deposit-qr",
    "signature": "0001"
    }
'
```

```json
// 200
{
  "error": "0",
  "code": "200",
  "message": "Request deposit success",
  "result": {
    "ref_id": "xxxx-xxxx-xxxx-xxxx",
    "order_id": "ORDER20230101001",
    "amount": "100.50",
    "image": "{api_url}/deposit/image/qrcode?token=xxxx-xxxx-xxxx-xxxx",
    "timeout": {
      "days": "YYYY-MM-DD",
      "time": "HH:mm:ss",
      "date": "2022-02-01T10:00:20.168Z"
    }
  }
}

//400
{
  "error": "1",
  "code": "400",
  "message": "Bad Request",
  "result": {}
}

//401
{
  "error": "1",
  "code": "401",
  "message": "Unauthorized. Please check you API_KEY or you signature is invalid.",
  "result": {}
}
```

## Deposit Callback

Support Event:

Deposit by QR Code
Deposit be Bank Transfer

-  SUCCESS:DEPOSIT_AUTO

```json
{
   "type": "DEPOSIT",
   "status": "SUCCESS",
   "status_code": "DEPOSIT_AUTO",
   "agent_confirm": "CONFIRM",
   "stm_ref_id": "d6c623ab-d62e812c-5597a81f-7bca944a",
   "stm_date": "YYYY-MM-DD hh:mm:ss",
   "stm_amount": "100.50",
   "stm_bank_name": "SCB",
   "stm_account_no": "**********",
   "stm_remark": "TR fr 004-********** โอนเงินเข้าพร้อมเพย์",
   "txn_ref_id": "ca3c3757-ffa8-49db-89df-e314cc5ecf60",
   "txn_order_id": "xxxxxxxx",
   "txn_user_id": "xxxxxxxx",  
   "deposit_balance": "100.00",
   "withdraw_balance": "0.00",
   "remark": "",
   "signature": "d95ba17d99c862a44ebb6f1c3039e6b4",
}
```

type: "DEPOSIT", "WITHDRAW"
status: "SUCCESS", "FAILED"

status_code:
For Deposit:

    DEPOSIT_NOT_MATCH : Unable to match the QR code transaction. (จับคู่รายการ QR Code ไม่ได้)
    DEPOSIT_AUTO : Successfully matched the QR code transaction. (จับคู่รายการ QR Code เรียบร้อย)
    DEPOSIT_AUTO_CUSTOMER : Successfully matched the transaction based on the customer's account number available in the system. (จับคู่รายการจากเลขบัญชีลูกค้าที่มีในระบบได้)
    TIMEOUT : The QR code has expired and is no longer valid for use. (QR Code หมดอายุการใช้งาน)
    CUST_CREATE_NEW_QR : (For status FAILED) This transaction QRCode failed because customer create new qr code.


## Withdraw
POST: /v1/withdraw

```bash
curl --request POST \
     --url https://xxxx.io/v1/withdraw \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
{
  "order_id": "002",
  "amount": "100",
  "to_account_no": "**********",
  "to_bank_code": "KBANK",
  "to_name": " สมชาย ใจดี",
  "callback_url": "https://payment-api-doc.readme.io/reference/withdraw",
  "signature": "test"
}
'
```

RESPONSE: 
```json
// 200
{
  "error": "0",
  "code": "200",
  "message": "Request withdraw success",
  "result": {
    "ref_id": "xxxx-xxxx-xxxx-xxxx",
    "order_id": "xxxxxxxxxx",
    "price": "100.00"
  }
}

//400
{
  "error": "1",
  "code": "400",
  "message": "Bad Request",
  "result": {}
}

//401
{
  "error": "1",
  "code": "401",
  "message": "Unauthorized. Please check you API_KEY or you signature is invalid.",
  "result": {}
}
```

## Withdraw Callback

Support Event:

    Withdraw
- SUCCESS

```json
{
  type: 'WITHDRAW',
  status: 'SUCCESS',
  status_code: 'OK',
  stm_ref_id: '',
  stm_date: '2023-10-27 14:00:00',
  stm_amount: '1000.00',
  stm_bank_name: '',
  stm_bank_code: '',
  stm_last_4account: '',
  stm_remark: '',
  txn_ref_id: 'xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx',
  txn_order_id: 'xxxxxxxx',
  txn_user_id: '',
  timestamp: '2023-10-27 14:02:46',
  account_no: '**********',
  account_bank_name: 'SCB',
  agent_confirm: '',
  stm_account_no: '',
  deposit_balance: '1.00',
  withdraw_balance: '1.00',
  remark: '',
  signature: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
}
```

- FAILED

```json
{
  type: 'WITHDRAW',
  status: 'FAILED',
  status_code: 'BANK_TRANSFER_FAIL',
  stm_ref_id: '',
  stm_date: '2023-10-27 14:00:00',
  stm_amount: '1000.00',
  stm_bank_name: '',
  stm_bank_code: '',
  stm_last_4account: '',
  stm_remark: '',
  txn_ref_id: 'xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx',
  txn_order_id: 'xxxxxxxx',
  txn_user_id: '',
  timestamp: '2023-10-27 14:02:46',
  account_no: '**********',
  account_bank_name: 'SCB',
  agent_confirm: '',
  stm_account_no: '',
  deposit_balance: '1.00',
  withdraw_balance: '1.00',
  remark: '',
  signature: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
}
```

## Order Inquiry

POST: /v1/order/inquiry

```bash
curl --request POST \
     --url https://xxxx.io/v1/order/inquiry \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
{
  "order_id": "003",
  "ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx"
}
'
```

RESPONSE:

```json
{
  "error": "0",
  "code": "200",
  "message": "Request success",
  "result": {
    "type": "DEPOSIT",
    "status": "SUCCESS",
    "status_code": "DEPOSIT_MATCH",
    "stm_ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
    "stm_date": "2023-05-13 22:02:15",
    "stm_amount": "1.00",
    "stm_bank_name": "",
    "stm_last_4account": "",
    "stm_remark": "รายการ Prompt-IN/นายสมชาย ใจดี",
    "txn_ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
    "txn_order_id": "ORDER20230513001TEST",
    "txn_user_id": "",
    "account_no": "**********",
    "account_bank_name": "SCB",
    "agent_confirm": "CONFIRM",
    "stm_account_no": ""
  }
}
```


## Request Callback

POST: /v1/request_callback


```bash
curl --request POST \
     --url https://xxxx.io/v1/request_callback \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
{
  "ref_id": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
  "order_id": "ORDERID",
  "signature": "f2cea7c847a7e20582195ecf8ce813ac"
}
'
```

RESPONSE:

```json
{
    "error": "0",
    "code": "200",
    "message": "Success",
    "result": []
  // ระบบจะทำการยิง callback กลับไปซ้ำอีกครั้งตาม url เดิมของ order นั้นๆ
}
```

## Get Bank List

GET: https://xxxx.io/v1/bank_list

```bash
curl --request GET \
     --url https://xxxx.io/v1/bank_list \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: undefined'
```

RESPONSE: 

```json
{
  "error": "0",
  "code": "200",
  "message": "OK",
  "result": [
    {
      "bank_type": "DEPOSIT",
      "bank_token": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
      "bank_name": "KBANK",
      "bank_code": "004",
      "bank_account_no": "**********",
      "bank_account_name": "สมชาย ใจดี"
    }
  ]
}
```

## Balance

POST: /v1/balance

```bash
curl --request POST \
     --url https://xxxx.io/v1/balance \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
{
  "bank_token": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
  "signature": "73542496c587b60a52fe40c23e047175"
}
'
```

RESPONSE:

```json
{
    "error": "0",
    "code": "200",
    "message": "Request balace success",
    "result":{
        "balance":"100.00", // DEPOSIT BALANCE
        "balance_withdraw":"0.00", // WITHDRAW BALANCE
        "date": "2023-02-01T10:11:43+07:00"
    }
}
```



## Bank Statement

POST: /v1/bank_statment

https://payment-api-doc.readme.io/reference/get-bank-statement

```bash
curl --request POST \
     --url https://xxxx.io/v1/bank_statment \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
{
  "date": "YYYY-MM-DD",
  "bank_token": "xxxxxxxx-xxxxxxxx-xxxxxxxx-xxxxxxxx",
  "signature": "73542496c587b60a52fe40c23e047175"
}
'
```

RESPONSE:

```json
{
  "error": "0",
  "code": "200",
  "message": "Request statusment success",
  "result": [
    {
      "txn_hash": "fb81901c-85e52eb6-27f827b5-fb6ca779",
      "account_no": "xxxxxxxxxx",
      "account_bank_code": "014",
      "account_bank_name": "SCB",
      "txn_amount": "1.00",
      "txn_date": "2023-02-16T09:29:20.000Z",
      "txn_remark": "กสิกรไทย (KBANK) /X820039",
      "txn_acc_4last": "820039",
      "txn_text_type": "ฝากเงิน",
      "txn_type": "DEPOSIT",
      "deposit_type": "TRANSFER",
      "created_date": "2023-02-16T09:31:06.000Z",
      "mdr_rate": "0.02",
      "mdr_amount": "0.02",
      "net_amount": "0.98"
    },
    {
      "txn_hash": "1ee49884-622bf209-ba57db2e-27e37985",
      "account_no": "xxxxxxxxxx",
      "account_bank_code": "014",
      "account_bank_name": "SCB",
      "txn_amount": "5.21",
      "txn_date": "2023-02-16T09:08:04.000Z",
      "txn_remark": "โอนไป KBANK x0039 นาย สมชายใ จดี",
      "txn_acc_4last": "0039",
      "txn_text_type": "ถอนเงิน",
      "txn_type": "WITHDRAW",
      "deposit_type": "",
      "created_date": "2023-02-16T09:09:06.000Z",
      "mdr_rate": "0.00",
      "mdr_amount": "0.00",
      "net_amount": "0.00"
    }
  ]
}
```

## Scan slip to verify

POST: /v1/scan_slip_verify

DOC URL: https://payment-api-doc.readme.io/reference/scan-slip-verify

```bash
curl --request POST \
     --url https://xxxx.io/v1/scan_slip_verify \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
{
  "qrtext": "string",
  "url_callback_slip": "string",
  "signature": "string"
}
'
```

RESPONSE:

```json
{
  "error": "0",
  "code": "200",
  "message": "Request deposit success",
  "result": {
    "order_hash": "xxxx-xxxx-xxxx-xxxx",
    "status": "CREATE",
    "from_bank_name": "XXXX",
    "from_account_no": "xxxx-xxxx",
    "from_account_name": "XXXX"
  }
}
```

## Scan slip to confirm

POST: /v1/scan_slip_confirm

DOC URL: https://payment-api-doc.readme.io/reference/scan-slip-confirm

```bash
curl --request POST \
     --url https://xxxx.io/v1/scan_slip_confirm \
     --header 'Content-Type: application/json' \
     --header 'accept: application/json' \
     --header 'authorization: Basic dGVzdDp0ZXN0' \
     --data '
{
  "order_hash": "string",
  "username": "string",
  "remark": "string",
  "callback_url": "string",
  "signature": "string"
}
'
```

RESPONSE:

```json
{
  "error": "0",
  "code": "200",
  "message": "Request deposit success",
  "result": {
    "order_hash": "xxxx-xxxx-xxxx-xxxx",
    "status": "FINISH",
    "used_datetime": "2023-12-12 20:00:00",
    "stm_ref_id": "zzzzz-zzzzz-zzzzzz-zzzzzz"
  }
}
```
