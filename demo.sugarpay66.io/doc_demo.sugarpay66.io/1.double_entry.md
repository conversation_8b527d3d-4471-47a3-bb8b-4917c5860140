# เอกสาร Database Schema - GalaxyPay Payment Gateway

## 📋 สารบัญ

1. [ภาพรวมระบบ](#ภาพรวมระบบ)
2. [โครงสร้างฐานข้อมูล](#โครงสร้างฐานข้อมูล)
3. [รายละเอียดตาราง](#รายละเอียดตาราง)
4. [ความสัมพันธ์ระหว่างตาราง](#ความสัมพันธ์ระหว่างตาราง)
5. [หลักการ Double Entry](#หลักการ-double-entry)
6. [การกระจายภาระ (Load Balancing)](#การกระจายภาระ-load-balancing)
7. [Views และ Functions](#views-และ-functions)
8. [การใช้งานและตัวอย่าง](#การใช้งานและตัวอย่าง)
9. [Performance และ Indexing](#performance-และ-indexing)
10. [การบำรุงรักษา](#การบำรุงรักษา)

---

## 🎯 ภาพรวมระบบ

GalaxyPay Payment Gateway เป็นระบบประมวลผลการชำระเงินที่ใช้หลักการ **Double Entry Bookkeeping** พร้อมระบบ **Load Balancing** สำหรับกระจายภาระบัญชีธนาคาร

### คุณสมบัติหลัก
- ✅ **Double Entry Accounting** - ความแม่นยำ 100%
- ✅ **Load Balancing** - กระจายภาระไปหลายบัญชีธนาคาร
- ✅ **Multi-Merchant Support** - รองรับร้านค้าหลายร้าน
- ✅ **Flexible MDR Configuration** - ตั้งค่าค่าธรรมเนียมได้ต่อร้าน
- ✅ **Real-time Balance Tracking** - ติดตามยอดคงเหลือแบบ Real-time
- ✅ **Bank API Integration** - เชื่อมต่อ API ธนาคารอัตโนมัติ

### Architecture Overview
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Bank APIs     │───▶│   GalaxyPay      │───▶│   Merchants     │
│                 │    │   Core System    │    │                 │
│ • Account 1     │    │                  │    │ • Coffee Shop   │
│ • Account 2     │    │ • Load Balancer  │    │ • Fashion Store │
│ • Account 3     │    │ • Double Entry   │    │ • Restaurant    │
│ • Account 4     │    │ • MDR Calculator │    │ • ...           │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

---

## 🗄️ โครงสร้างฐานข้อมูล

### จำนวนตาราง: 8 Tables หลัก

| ลำดับ | ตาราง | จุดประสงค์ | ขนาดโดยประมาณ |
|-------|-------|------------|----------------|
| 1 | `bank_accounts` | บัญชีธนาคารของระบบ | 10-50 records |
| 2 | `merchants` | ข้อมูลร้านค้า | 100-10,000 records |
| 3 | `bank_accounts_merchants` | การจัดสรรบัญชีให้ร้านค้า | 200-50,000 records |
| 4 | `bank_transactions` | รายการธนาคาร | 1,000-1,000,000+ records |
| 5 | `transactions` | รายการธุรกรรมหลัก | 1,000-1,000,000+ records |
| 6 | `accounts` | ผังบัญชี | 10-50 records |
| 7 | `journal_entries` | รายการบัญชี | 1,000-1,000,000+ records |
| 8 | `journal_lines` | รายการย่อยบัญชี | 4,000-4,000,000+ records |

### Database Schema Diagram
```
┌─────────────────┐    ┌─────────────────────┐    ┌─────────────────┐
│  bank_accounts  │    │bank_accounts_       │    │   merchants     │
│                 │◄──┤merchants            │───▶│                 │
│ • id            │    │                     │    │ • id            │
│ • account_number│    │ • merchant_id       │    │ • merchant_code │
│ • bank_name     │    │ • bank_account_id   │    │ • business_name │
│ • account_type  │    │ • priority          │    │ • deposit_bal   │
│ • current_bal   │    │ • weight_percent    │    │ • withdraw_bal  │
└─────────────────┘    └─────────────────────┘    └─────────────────┘
         │                                                    │
         │              ┌─────────────────┐                   │
         └─────────────▶│  transactions   │◄──────────────────┘
                        │                 │
                        │ • id            │
                        │ • merchant_id   │
                        │ • gross_amount  │
                        │ • mdr_fee       │
                        │ • net_amount    │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │ journal_entries │
                        │                 │
                        │ • id            │
                        │ • transaction_id│
                        │ • total_amount  │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐    ┌─────────────────┐
                        │ journal_lines   │───▶│   accounts      │
                        │                 │    │                 │
                        │ • journal_entry │    │ • id            │
                        │ • account_id    │    │ • account_code  │
                        │ • debit_amount  │    │ • account_name  │
                        │ • credit_amount │    │ • account_type  │
                        └─────────────────┘    └─────────────────┘
```

---

## 📊 รายละเอียดตาราง

### 1. `bank_accounts` - บัญชีธนาคารของระบบ

**จุดประสงค์:** เก็บข้อมูลบัญชีธนาคารที่ระบบใช้รับ-จ่ายเงิน

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `account_number` | VARCHAR(50) UNIQUE | เลขที่บัญชี | '123-4-56789-0' |
| `account_name` | VARCHAR(100) | ชื่อบัญชี | 'GalaxyPay Deposit 1' |
| `bank_name` | VARCHAR(100) | ธนาคาร | 'กสิกรไทย' |
| `account_type` | ENUM | ประเภท | 'DEPOSIT', 'WITHDRAW', 'SAVINGS' |
| `before_balance` | DECIMAL(15,2) | ยอดก่อนหน้า | 100000.00 |
| `current_balance` | DECIMAL(15,2) | ยอดปัจจุบัน | 105000.00 |
| `daily_limit` | DECIMAL(15,2) | วงเงินต่อวัน | 1000000.00 |
| `monthly_limit` | DECIMAL(15,2) | วงเงินต่อเดือน | ********.00 |
| `current_daily_usage` | DECIMAL(15,2) | ยอดใช้วันนี้ | 50000.00 |
| `current_monthly_usage` | DECIMAL(15,2) | ยอดใช้เดือนนี้ | 500000.00 |
| `is_active` | BOOLEAN | สถานะใช้งาน | TRUE/FALSE |
| `api_config` | JSON | การตั้งค่า API | '{"endpoint": "...", "key": "..."}' |

**Business Rules:**
- บัญชี DEPOSIT ใช้รับเงินจากลูกค้า
- บัญชี WITHDRAW ใช้จ่ายเงินให้ร้านค้า
- บัญชี SAVINGS ใช้เก็บเงินสำรอง
- ต้องมี `daily_limit` และ `monthly_limit` เพื่อควบคุมความเสี่ยง

### 2. `merchants` - ข้อมูลร้านค้า

**จุดประสงค์:** เก็บข้อมูลร้านค้าและการตั้งค่าค่าธรรมเนียม

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `merchant_code` | VARCHAR(20) UNIQUE | รหัสร้านค้า | 'COFFEE001' |
| `business_name` | VARCHAR(200) | ชื่อร้านค้า | 'ร้านกาแฟดี' |
| `contact_name` | VARCHAR(100) | ชื่อผู้ติดต่อ | 'คุณสมศักดิ์' |
| `email` | VARCHAR(100) | อีเมล | '<EMAIL>' |
| `phone` | VARCHAR(20) | เบอร์โทร | '02-123-4567' |
| `deposit_balance` | DECIMAL(15,2) | ยอดเงินฝาก | 5000.00 |
| `withdraw_balance` | DECIMAL(15,2) | ยอดเงินถอน | 2000.00 |
| `mdr_deposit_rate` | DECIMAL(5,4) | ค่าธรรมเนียม % ฝาก | 1.5000 (1.5%) |
| `mdr_withdraw_fee` | DECIMAL(10,2) | ค่าธรรมเนียม ถอน | 10.00 |
| `mdr_topup_rate` | DECIMAL(5,4) | ค่าธรรมเนียม % เติมเงิน | 1.5000 |
| `mdr_settlement_fee` | DECIMAL(10,2) | ค่าธรรมเนียม ส่งเงิน | 10.00 |
| `daily_deposit_limit` | DECIMAL(15,2) | วงเงินฝากต่อวัน | 500000.00 |
| `daily_withdraw_limit` | DECIMAL(15,2) | วงเงินถอนต่อวัน | 100000.00 |

**Business Rules:**
- `deposit_balance` = เงินที่ร้านค้าสามารถถอนหรือโยกได้
- `withdraw_balance` = เงินที่ร้านค้าสามารถขอส่งเงินได้
- MDR Rate แยกตามประเภทธุรกรรม
- แต่ละร้านสามารถตั้งค่า MDR ได้แยกจากค่า Default

### 3. `bank_accounts_merchants` - การจัดสรรบัญชีให้ร้านค้า

**จุดประสงค์:** กำหนดว่าร้านค้าแต่ละร้านใช้บัญชีธนาคารไหน และการกระจายภาระ

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `merchant_id` | INT FK | รหัสร้านค้า | 1 |
| `bank_account_id` | INT FK | รหัสบัญชีธนาคาร | 2 |
| `account_type` | ENUM | ประเภทการใช้งาน | 'DEPOSIT', 'WITHDRAW' |
| `priority` | INT | ลำดับความสำคัญ | 1 (หลัก), 2 (สำรอง) |
| `weight_percent` | DECIMAL(5,2) | สัดส่วนการกระจาย | 60.00 (60%) |
| `is_active` | BOOLEAN | สถานะใช้งาน | TRUE/FALSE |

**ตัวอย่างการกระจายภาระ:**
```sql
-- ร้านกาแฟดี ใช้ 2 บัญชีฝาก
INSERT INTO bank_accounts_merchants VALUES
(1, 1, 1, 'DEPOSIT', 1, 60.00, TRUE), -- กสิกรไทย 60%
(2, 1, 2, 'DEPOSIT', 2, 40.00, TRUE); -- ไทยพาณิชย์ 40%
```

**Business Rules:**
- Priority 1 = บัญชีหลัก, Priority 2+ = บัญชีสำรอง
- Weight Percent รวมไม่จำเป็นต้องเป็น 100%
- ระบบจะเลือกบัญชีตาม Priority แล้วตาม Weight
- สามารถเพิ่ม-ลดบัญชีได้โดยการเปลี่ยน `is_active`

### 4. `bank_transactions` - รายการธนาคาร

**จุดประสงค์:** เก็บรายการเงินเข้า-ออกจากบัญชีธนาคาร (ดึงจาก Bank API)

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `bank_account_id` | INT FK | รหัสบัญชีธนาคาร | 1 |
| `transaction_ref` | VARCHAR(100) | เลขที่อ้างอิงธนาคาร | 'BT20250622001' |
| `transaction_date` | TIMESTAMP | วันเวลารายการ | '2025-06-22 08:30:00' |
| `transaction_type` | ENUM | ประเภท | 'CREDIT' (เข้า), 'DEBIT' (ออก) |
| `amount` | DECIMAL(15,2) | จำนวนเงิน | 150.00 |
| `description` | TEXT | รายละเอียด | 'คุณสมชาย ซื้อกาแฟ' |
| `from_account` | VARCHAR(100) | บัญชีต้นทาง | 'SCB-xxx-1234' |
| `to_account` | VARCHAR(100) | บัญชีปลายทาง | '123-4-56789-0' |
| `is_processed` | BOOLEAN | สถานะประมวลผล | TRUE/FALSE |

**Business Rules:**
- ข้อมูลมาจาก Bank API (Real-time หรือ Batch)
- `transaction_ref` ต้อง Unique ต่อบัญชี
- `is_processed = FALSE` = ยังไม่สร้าง Transaction
- `is_processed = TRUE` = สร้าง Transaction แล้ว

### 5. `transactions` - รายการธุรกรรมหลัก

**จุดประสงค์:** รายการธุรกรรมหลักของระบบ หลังจากประมวลผล Bank Transaction

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `transaction_ref` | VARCHAR(100) UNIQUE | เลขที่อ้างอิงระบบ | 'TXN20250622001' |
| `merchant_id` | INT FK | รหัสร้านค้า | 1 |
| `bank_transaction_id` | INT FK | รหัส Bank Transaction | 1 |
| `assigned_bank_account_id` | INT FK | บัญชีที่ถูกเลือกใช้ | 1 |
| `transaction_type` | ENUM | ประเภทธุรกรรม | 'DEPOSIT', 'WITHDRAW', 'TOPUP', 'TRANSFER', 'SETTLEMENT' |
| `gross_amount` | DECIMAL(15,2) | ยอดก่อนหักค่าธรรมเนียม | 150.00 |
| `mdr_fee` | DECIMAL(15,2) | ค่าธรรมเนียม MDR | 2.25 |
| `net_amount` | DECIMAL(15,2) | ยอดหลังหักค่าธรรมเนียม | 147.75 |
| `status` | ENUM | สถานะ | 'PENDING', 'COMPLETED', 'FAILED', 'CANCELLED' |
| `description` | TEXT | รายละเอียด | 'กาแฟอเมริกาโน + ขนมปัง' |
| `metadata` | JSON | ข้อมูลเพิ่มเติม | '{"customer": "คุณสมชาย", "items": [...]}' |

**ประเภทธุรกรรม:**
- **DEPOSIT:** ลูกค้าชำระเงิน → เข้า Deposit Balance
- **WITHDRAW:** ร้านค้าถอนเงิน → ออกจาก Withdraw Balance
- **TOPUP:** เติมเงินเข้า Withdraw Balance โดยตรง
- **TRANSFER:** โยกเงิน Deposit → Withdraw Balance
- **SETTLEMENT:** ส่งเงินให้ร้านค้าอัตโนมัติ

### 6. `accounts` - ผังบัญชี

**จุดประสงค์:** ผังบัญชีสำหรับระบบ Double Entry Bookkeeping

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `account_code` | VARCHAR(20) UNIQUE | รหัสบัญชี | '1100', '1200' |
| `account_name` | VARCHAR(100) | ชื่อบัญชี | 'Bank Cash' |
| `account_type` | ENUM | ประเภทบัญชี | 'ASSET', 'LIABILITY', 'REVENUE', 'EXPENSE' |
| `normal_balance` | ENUM | ยอดปกติ | 'DEBIT', 'CREDIT' |
| `current_balance` | DECIMAL(15,2) | ยอดปัจจุบัน | 150000.00 |

**ผังบัญชีมาตรฐาน:**
| รหัส | ชื่อบัญชี | ประเภท | Normal Balance |
|------|-----------|--------|----------------|
| 1100 | Bank Cash | ASSET | DEBIT |
| 1200 | Merchant Deposit Balances | ASSET | DEBIT |
| 1300 | Merchant Withdraw Balances | ASSET | DEBIT |
| 2100 | Merchant Payables | LIABILITY | CREDIT |
| 4100 | MDR Fee Revenue | REVENUE | CREDIT |
| 5100 | Bank Service Charges | EXPENSE | DEBIT |

### 7. `journal_entries` - รายการบัญชี

**จุดประสงค์:** รายการบัญชีหลักสำหรับ Double Entry

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `journal_ref` | VARCHAR(100) UNIQUE | เลขที่รายการบัญชี | 'JE20250622001' |
| `transaction_id` | INT FK | รหัส Transaction | 1 |
| `entry_date` | DATE | วันที่บันทึก | '2025-06-22' |
| `description` | TEXT | คำอธิบาย | 'คุณสมชาย โอน 150 บาท' |
| `total_amount` | DECIMAL(15,2) | ยอดรวม | 150.00 |
| `status` | ENUM | สถานะ | 'DRAFT', 'POSTED' |

### 8. `journal_lines` - รายการย่อยบัญชี

**จุดประสงค์:** รายการย่อยของแต่ละรายการบัญชี (Double Entry Lines)

| Field | Type | คำอธิบาย | ตัวอย่าง |
|-------|------|----------|----------|
| `id` | INT PRIMARY KEY | รหัสอ้างอิง | 1, 2, 3 |
| `journal_entry_id` | INT FK | รหัสรายการบัญชี | 1 |
| `account_id` | INT FK | รหัสบัญชี | 1 |
| `debit_amount` | DECIMAL(15,2) | ยอด Debit | 150.00 |
| `credit_amount` | DECIMAL(15,2) | ยอด Credit | 0.00 |
| `description` | TEXT | คำอธิบาย | 'เงินเข้าบัญชีธนาคาร' |
| `reference_type` | ENUM | ประเภทอ้างอิง | 'merchant', 'bank_account', 'system' |
| `reference_id` | INT | รหัสอ้างอิง | 1 |
| `line_number` | INT | เลขที่บรรทัด | 1, 2, 3, 4 |

**ตัวอย่าง Journal Lines สำหรับ Deposit 150 บาท:**
| Line | Account | Debit | Credit | Description |
|------|---------|-------|--------|-------------|
| 1 | Bank Cash (1100) | 150.00 | 0.00 | เงินเข้าธนาคาร |
| 2 | Merchant Deposit (1200) | 147.75 | 0.00 | ยอดสุทธิเข้าร้าน |
| 3 | MDR Revenue (4100) | 2.25 | 0.00 | รายได้ค่าธรรมเนียม |
| 4 | Merchant Payables (2100) | 0.00 | 150.00 | หนี้สินต่อร้าน |

---

## 🔗 ความสัมพันธ์ระหว่างตาราง

### Foreign Key Relationships

```sql
-- merchants ←→ bank_accounts_merchants
bank_accounts_merchants.merchant_id → merchants.id

-- bank_accounts ←→ bank_accounts_merchants  
bank_accounts_merchants.bank_account_id → bank_accounts.id

-- bank_accounts ←→ bank_transactions
bank_transactions.bank_account_id → bank_accounts.id

-- merchants ←→ transactions
transactions.merchant_id → merchants.id

-- bank_transactions ←→ transactions
transactions.bank_transaction_id → bank_transactions.id

-- bank_accounts ←→ transactions
transactions.assigned_bank_account_id → bank_accounts.id

-- transactions ←→ journal_entries
journal_entries.transaction_id → transactions.id

-- journal_entries ←→ journal_lines
journal_lines.journal_entry_id → journal_entries.id

-- accounts ←→ journal_lines
journal_lines.account_id → accounts.id
```

### Cardinality
- **1:N** - merchants : bank_accounts_merchants
- **1:N** - bank_accounts : bank_accounts_merchants
- **1:N** - bank_accounts : bank_transactions
- **1:N** - merchants : transactions
- **1:1** - bank_transactions : transactions
- **1:1** - transactions : journal_entries
- **1:N** - journal_entries : journal_lines
- **1:N** - accounts : journal_lines

---

## 📝 หลักการ Double Entry

### กฎพื้นฐาน
1. **ทุกรายการต้องมี Debit = Credit**
2. **Account Types มี Normal Balance ที่แตกต่างกัน**

### ตัวอย่าง Double Entry สำหรับ Deposit 150 บาท

```sql
-- สร้าง Journal Entry
INSERT INTO journal_entries VALUES
(1, 'JE20250622001', 1, '2025-06-22', 'Deposit 150 บาท', 150.00, 'POSTED');

-- สร้าง Journal Lines (Debit = Credit = 150.00)
INSERT INTO journal_lines VALUES
(1, 1, 1, 150.00, 0.00, 'เงินเข้าธนาคาร', 'bank_account', 1, 1),        -- Dr. Bank Cash
(2, 1, 2, 147.75, 0.00, 'ยอดสุทธิเข้าร้าน', 'merchant', 1, 2),          -- Dr. Merchant Deposit  
(3, 1, 5, 2.25, 0.00, 'ค่าธรรมเนียม MDR', 'system', null, 3),           -- Dr. MDR Revenue
(4, 1, 4, 0.00, 150.00, 'หนี้สินต่อร้าน', 'merchant', 1, 4);             -- Cr. Merchant Payable
```

### การตรวจสอบความถูกต้อง

```sql
-- ตรวจสอบ Trial Balance (ต้องได้ Difference = 0)
SELECT 
    SUM(debit_amount) as Total_Debit,
    SUM(credit_amount) as Total_Credit,
    SUM(debit_amount) - SUM(credit_amount) as Difference
FROM journal_lines jl
JOIN journal_entries je ON jl.journal_entry_id = je.id
WHERE je.status = 'POSTED';
```

---

## ⚖️ การกระจายภาระ (Load Balancing)

### หลักการทำงาน

1. **Priority-based Selection**
   - Priority 1 = บัญชีหลัก
   - Priority 2+ = บัญชีสำรอง

2. **Weight-based Distribution**
   - กระจายภาระตาม % ที่กำหนด
   - ระบบจะเลือกบัญชีตามน้ำหัก

3. **Capacity Checking**
   - ตรวจสอบวงเงินวันนี้
   - ตรวจสอบยอดคงเหลือ

### Algorithm การเลือกบัญชี

```sql
-- Function: เลือกบัญชีฝาก
DELIMITER //
CREATE FUNCTION select_deposit_bank_account(
    p_merchant_id INT,
    p_amount DECIMAL(15,2)
) RETURNS INT
BEGIN
    -- 1. เลือกบัญชีที่ยังไม่เกินวงเงิน
    -- 2. เรียงตาม Priority ASC
    -- 3. เรียงตาม Current Usage ASC
    -- 4. Return bank_account_id
END //
```

### ตัวอย่างการกระจายภาระ

**ร้านเสื้อผ้า (Priority และ Weight):**
```sql
INSERT INTO bank_accounts_merchants VALUES
(1, 2, 2, 'DEPOSIT', 1, 50.00, TRUE), -- ไทยพาณิชย์ Priority 1, 50%
(2, 2, 3, 'DEPOSIT', 2, 30.00, TRUE), -- กรุงเทพ Priority 2, 30%  
(3, 2, 4, 'DEPOSIT', 3, 20.00, TRUE); -- กรุงไทย Priority 3, 20%
```

**ผลลัพธ์การกระจาย:**
- ลูกค้าโอน 1,200 บาท → ไทยพาณิชย์ (Priority 1)
- ลูกค้าโอน 850 บาท → กรุงเทพ (Priority 2, Load Balance)
- ลูกค้าโอน 680 บาท → กรุงไทย (Priority 3, Load Balance)

---

## 📊 Views และ Functions

### Views สำหรับ Reporting

#### 1. `view_trial_balance` - Trial Balance
```sql
CREATE VIEW view_trial_balance AS
SELECT 
    a.account_code,
    a.account_name,
    a.account_type,
    SUM(jl.debit_amount) as total_debit,
    SUM(jl.credit_amount) as total_credit,
    SUM(jl.debit_amount) - SUM(jl.credit_amount) as balance
FROM accounts a
LEFT JOIN journal_lines jl ON a.id = jl.account_id
LEFT JOIN journal_entries je ON jl.journal_entry_id = je.id
WHERE je.status = 'POSTED'
GROUP BY a.id
ORDER BY a.account_code;
```

#### 2. `view_merchant_summary` - สรุปข้อมูลร้านค้า
```sql
CREATE VIEW view_merchant_summary AS
SELECT 
    m.merchant_code,
    m.business_name,
    m.deposit_balance,
    m.withdraw_balance,
    m.deposit_balance + m.withdraw_balance as total_balance,
    COUNT(t.id) as transaction_count,
    SUM(CASE WHEN t.status = 'COMPLETED' THEN t.gross_amount ELSE 0 END) as total_gross,
    SUM(CASE WHEN t.status = 'COMPLETED' THEN t.mdr_fee ELSE 0 END) as total_mdr_fee,
    SUM(CASE WHEN t.status = 'COMPLETED' THEN t.net_amount ELSE 0 END) as total_net
FROM merchants m
LEFT JOIN transactions t ON m.id = t.merchant_id
GROUP BY m.id;
```

#### 3. `view_bank_usage_summary` - สรุปการใช้งานบัญชี
```sql
CREATE VIEW view_bank_usage_summary AS
SELECT 
    ba.account_number,
    ba.bank_name,
    ba.account_type,
    ba.current_balance,
    ba.daily_limit,
    ba.current_daily_usage,
    ROUND((ba.current_daily_usage / ba.daily_limit) * 100, 2) as daily_usage_percent,
    COUNT(bam.id) as assigned_merchants,
    ba.is_active
FROM bank_accounts ba
LEFT JOIN bank_accounts_merchants bam ON ba.id = bam.bank_account_id AND bam.is_active = TRUE
GROUP BY ba.id
ORDER BY ba.account_type, daily_usage_percent DESC;
```

#### 4. `view_merchant_bank_accounts` - การจัดสรรบัญชี
```sql
CREATE VIEW view_merchant_bank_accounts AS
SELECT 
    m.merchant_code,
    m.business_name,
    ba.account_number,
    ba.bank_name,
    ba.account_type,
    bam.account_type as usage_type,
    bam.priority,
    bam.weight_percent,
    ba.current_balance,
    ba.daily_limit,
    ba.current_daily_usage,
    (ba.daily_limit - ba.current_daily_usage) as remaining_daily_limit,
    bam.is_active
FROM merchants m
JOIN bank_accounts_merchants bam ON m.id = bam.merchant_id
JOIN bank_accounts ba ON bam.bank_account_id = ba.id
ORDER BY m.merchant_code, bam.account_type, bam.priority;
```

### Functions สำหรับ Business Logic

#### 1. `calculate_mdr_fee()` - คำนวนค่าธรรมเนียม
```sql
DELIMITER //
CREATE FUNCTION calculate_mdr_fee(
    merchant_id INT,
    transaction_type VARCHAR(20),
    amount DECIMAL(15,2)
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE fee DECIMAL(15,2) DEFAULT 0.00;
    DECLARE rate DECIMAL(5,4);
    DECLARE fixed_fee DECIMAL(10,2);
    
    SELECT 
        CASE transaction_type
            WHEN 'DEPOSIT' THEN mdr_deposit_rate
            WHEN 'TOPUP' THEN mdr_topup_rate
            ELSE 0.0000
        END,
        CASE transaction_type
            WHEN 'WITHDRAW' THEN mdr_withdraw_fee
            WHEN 'SETTLEMENT' THEN mdr_settlement_fee
            ELSE 0.00
        END
    INTO rate, fixed_fee
    FROM merchants 
    WHERE id = merchant_id;
    
    IF transaction_type IN ('DEPOSIT', 'TOPUP') THEN
        SET fee = amount * rate / 100;
    ELSEIF transaction_type IN ('WITHDRAW', 'SETTLEMENT') THEN
        SET fee = fixed_fee;
    END IF;
    
    RETURN fee;
END //
DELIMITER ;
```

#### 2. `select_deposit_bank_account()` - เลือกบัญชีฝาก
```sql
DELIMITER //
CREATE FUNCTION select_deposit_bank_account(
    p_merchant_id INT,
    p_amount DECIMAL(15,2)
) RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_bank_account_id INT DEFAULT NULL;
    
    -- เลือกบัญชีที่ยังไม่เกินวงเงิน และมี Priority ต่ำสุด
    SELECT ba.id INTO v_bank_account_id
    FROM bank_accounts_merchants bam
    JOIN bank_accounts ba ON bam.bank_account_id = ba.id
    WHERE bam.merchant_id = p_merchant_id 
    AND bam.account_type = 'DEPOSIT'
    AND bam.is_active = TRUE
    AND ba.is_active = TRUE
    AND (ba.current_daily_usage + p_amount) <= ba.daily_limit
    ORDER BY bam.priority ASC, ba.current_daily_usage ASC
    LIMIT 1;
    
    -- ถ้าไม่มีบัญชีที่เหมาะสม ให้ใช้บัญชีหลัก
    IF v_bank_account_id IS NULL THEN
        SELECT ba.id INTO v_bank_account_id
        FROM bank_accounts_merchants bam
        JOIN bank_accounts ba ON bam.bank_account_id = ba.id
        WHERE bam.merchant_id = p_merchant_id 
        AND bam.account_type = 'DEPOSIT'
        AND bam.is_active = TRUE
        AND ba.is_active = TRUE
        ORDER BY bam.priority ASC
        LIMIT 1;
    END IF;
    
    RETURN v_bank_account_id;
END //
DELIMITER ;
```

### Stored Procedures

#### 1. `create_deposit_transaction()` - สร้างรายการฝาก
```sql
DELIMITER //
CREATE PROCEDURE create_deposit_transaction(
    IN p_merchant_id INT,
    IN p_bank_transaction_id INT,
    IN p_gross_amount DECIMAL(15,2),
    IN p_description TEXT
)
BEGIN
    DECLARE v_transaction_id INT;
    DECLARE v_journal_id INT;
    DECLARE v_mdr_fee DECIMAL(15,2);
    DECLARE v_net_amount DECIMAL(15,2);
    DECLARE v_selected_bank_account INT;
    
    -- เลือกบัญชีธนาคาร
    SET v_selected_bank_account = select_deposit_bank_account(p_merchant_id, p_gross_amount);
    
    -- คำนวนค่าธรรมเนียม
    SET v_mdr_fee = calculate_mdr_fee(p_merchant_id, 'DEPOSIT', p_gross_amount);
    SET v_net_amount = p_gross_amount - v_mdr_fee;
    
    -- บันทึก Transaction
    INSERT INTO transactions (transaction_ref, merchant_id, bank_transaction_id, 
                            assigned_bank_account_id, transaction_type, gross_amount, 
                            mdr_fee, net_amount, status, description)
    VALUES (CONCAT('TXN', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')), p_merchant_id, 
            p_bank_transaction_id, v_selected_bank_account, 'DEPOSIT', 
            p_gross_amount, v_mdr_fee, v_net_amount, 'COMPLETED', p_description);
    
    SET v_transaction_id = LAST_INSERT_ID();
    
    -- บันทึก Journal Entry
    INSERT INTO journal_entries (journal_ref, transaction_id, entry_date, description, total_amount)
    VALUES (CONCAT('JE', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')), v_transaction_id, 
            CURDATE(), p_description, p_gross_amount);
    
    SET v_journal_id = LAST_INSERT_ID();
    
    -- บันทึก Journal Lines (Double Entry)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, 
                              description, reference_type, reference_id, line_number) VALUES
    (v_journal_id, 1, p_gross_amount, 0.00, 'เงินเข้าธนาคาร', 'bank_account', v_selected_bank_account, 1),
    (v_journal_id, 2, v_net_amount, 0.00, 'ยอดสุทธิเข้าร้าน', 'merchant', p_merchant_id, 2),
    (v_journal_id, 5, v_mdr_fee, 0.00, 'รายได้ค่าธรรมเนียม', 'system', NULL, 3),
    (v_journal_id, 4, 0.00, p_gross_amount, 'หนี้สินต่อร้าน', 'merchant', p_merchant_id, 4);
    
    -- อัพเดต Merchant Balance
    UPDATE merchants 
    SET deposit_balance = deposit_balance + v_net_amount
    WHERE id = p_merchant_id;
    
    -- อัพเดต Bank Account Usage
    UPDATE bank_accounts 
    SET current_daily_usage = current_daily_usage + p_gross_amount,
        current_balance = current_balance + p_gross_amount
    WHERE id = v_selected_bank_account;
    
END //
DELIMITER ;
```

---

## 🚀 การใช้งานและตัวอย่าง

### การใช้งานพื้นฐาน

#### 1. สร้างร้านค้าใหม่
```sql
-- เพิ่มร้านค้า
INSERT INTO merchants (merchant_code, business_name, contact_name, email, phone, 
                      deposit_balance, withdraw_balance) VALUES
('PIZZA001', 'ร้านพิซซ่าอร่อย', 'คุณพิซซ่า', '<EMAIL>', '02-111-2222', 0.00, 0.00);

-- กำหนดบัญชีธนาคาร (Load Balancing)
INSERT INTO bank_accounts_merchants (merchant_id, bank_account_id, account_type, priority, weight_percent) VALUES
(3, 1, 'DEPOSIT', 1, 70.00), -- กสิกรไทย หลัก 70%
(3, 3, 'DEPOSIT', 2, 30.00), -- กรุงเทพ สำรอง 30%
(3, 5, 'WITHDRAW', 1, 100.00); -- กสิกรไทย ถอน 100%
```

#### 2. ประมวลผลรายการฝาก
```sql
-- เมื่อมีรายการธนาคารเข้า
INSERT INTO bank_transactions (bank_account_id, transaction_ref, transaction_date, 
                              transaction_type, amount, description, from_account) VALUES
(1, 'BT20250622011', NOW(), 'CREDIT', 500.00, 'คุณโน้ต ซื้อพิซซ่า', 'SCB-xxx-9999');

-- สร้างรายการธุรกรรม (ใช้ Procedure)
CALL create_deposit_transaction(3, LAST_INSERT_ID(), 500.00, 'พิซซ่าฮาวายเอียน');
```

#### 3. ตรวจสอบผลลัพธ์
```sql
-- ดูสรุปร้านค้า
SELECT * FROM view_merchant_summary WHERE merchant_code = 'PIZZA001';

-- ดู Trial Balance
SELECT * FROM view_trial_balance;

-- ดูการกระจายภาระ
SELECT * FROM view_bank_usage_summary;
```

### ตัวอย่างการ Query ข้อมูล

#### 1. รายงานรายวัน
```sql
-- รายงานยอดรวมรายวัน
SELECT 
    DATE(t.created_at) as วันที่,
    COUNT(*) as จำนวนรายการ,
    SUM(t.gross_amount) as ยอดโอนรวม,
    SUM(t.mdr_fee) as ค่าธรรมเนียมรวม,
    SUM(t.net_amount) as ยอดสุทธิรวม
FROM transactions t 
WHERE t.status = 'COMPLETED'
AND DATE(t.created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(t.created_at)
ORDER BY วันที่ DESC;
```

#### 2. รายงานการกระจายภาระ
```sql
-- การกระจายภาระรายวัน
SELECT 
    ba.bank_name as ธนาคาร,
    COUNT(t.id) as จำนวนรายการ,
    SUM(t.gross_amount) as ยอดรวม,
    ROUND(AVG(t.gross_amount), 2) as ยอดเฉลี่ย,
    ROUND((SUM(t.gross_amount) / (SELECT SUM(gross_amount) FROM transactions WHERE status = 'COMPLETED' AND DATE(created_at) = CURDATE())) * 100, 2) as เปอร์เซ็นต์
FROM transactions t
JOIN bank_accounts ba ON t.assigned_bank_account_id = ba.id
WHERE t.status = 'COMPLETED' AND DATE(t.created_at) = CURDATE()
GROUP BY ba.id
ORDER BY ยอดรวม DESC;
```

#### 3. รายงานประสิทธิภาพร้านค้า
```sql
-- ประสิทธิภาพร้านค้า (รายเดือน)
SELECT 
    m.merchant_code as รหัสร้าน,
    m.business_name as ชื่อร้าน,
    COUNT(t.id) as จำนวนรายการ,
    SUM(t.gross_amount) as ยอดขาย,
    SUM(t.mdr_fee) as ค่าธรรมเนียม,
    ROUND(AVG(t.gross_amount), 2) as ยอดเฉลี่ยต่อรายการ,
    ROUND((SUM(t.mdr_fee) / SUM(t.gross_amount)) * 100, 4) as เปอร์เซ็นต์ธรรมเนียม
FROM merchants m
LEFT JOIN transactions t ON m.id = t.merchant_id 
WHERE t.status = 'COMPLETED' 
AND YEAR(t.created_at) = YEAR(CURDATE())
AND MONTH(t.created_at) = MONTH(CURDATE())
GROUP BY m.id
HAVING COUNT(t.id) > 0
ORDER BY ยอดขาย DESC;
```

---

## ⚡ Performance และ Indexing

### Indexes ที่สำคัญ

```sql
-- Primary Indexes (สร้างไว้แล้ว)
CREATE INDEX idx_bank_accounts_merchants_merchant ON bank_accounts_merchants(merchant_id);
CREATE INDEX idx_bank_accounts_merchants_bank ON bank_accounts_merchants(bank_account_id);
CREATE INDEX idx_bank_accounts_merchants_type ON bank_accounts_merchants(account_type, is_active);

-- Transaction Indexes
CREATE INDEX idx_bank_transactions_date ON bank_transactions(transaction_date);
CREATE INDEX idx_bank_transactions_processed ON bank_transactions(is_processed);
CREATE INDEX idx_transactions_merchant ON transactions(merchant_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_date ON transactions(created_at);

-- Journal Indexes
CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_journal_entries_status ON journal_entries(status);
CREATE INDEX idx_journal_lines_account ON journal_lines(account_id);
CREATE INDEX idx_journal_lines_reference ON journal_lines(reference_type, reference_id);

-- Composite Indexes สำหรับ Query ที่ใช้บ่อย
CREATE INDEX idx_transactions_merchant_status_date ON transactions(merchant_id, status, created_at);
CREATE INDEX idx_journal_lines_entry_account ON journal_lines(journal_entry_id, account_id);
CREATE INDEX idx_bank_transactions_account_date ON bank_transactions(bank_account_id, transaction_date);
```

### Performance Tips

#### 1. Query Optimization
```sql
-- ❌ Slow Query
SELECT * FROM transactions WHERE DATE(created_at) = CURDATE();

-- ✅ Fast Query  
SELECT * FROM transactions 
WHERE created_at >= CURDATE() AND created_at < DATE_ADD(CURDATE(), INTERVAL 1 DAY);
```

#### 2. Batch Processing
```sql
-- ประมวลผล Bank Transactions แบบ Batch
UPDATE bank_transactions 
SET is_processed = TRUE 
WHERE id IN (
    SELECT id FROM (
        SELECT id FROM bank_transactions 
        WHERE is_processed = FALSE 
        LIMIT 1000
    ) as batch
);
```

#### 3. Archive Strategy
```sql
-- Archive รายการเก่า (> 1 ปี)
CREATE TABLE transactions_archive LIKE transactions;
CREATE TABLE journal_entries_archive LIKE journal_entries;
CREATE TABLE journal_lines_archive LIKE journal_lines;

-- ย้ายข้อมูลเก่า
INSERT INTO transactions_archive 
SELECT * FROM transactions 
WHERE created_at < DATE_SUB(CURDATE(), INTERVAL 1 YEAR);
```

---

## 🔧 การบำรุงรักษา

### Daily Maintenance

#### 1. ตรวจสอบ Trial Balance
```sql
-- ต้องได้ Difference = 0 เสมอ
SELECT 
    SUM(debit_amount) - SUM(credit_amount) as difference,
    CASE 
        WHEN SUM(debit_amount) - SUM(credit_amount) = 0 THEN '✅ OK'
        ELSE '❌ ERROR'
    END as status
FROM journal_lines jl
JOIN journal_entries je ON jl.journal_entry_id = je.id
WHERE je.status = 'POSTED'
AND DATE(je.entry_date) = CURDATE();
```

#### 2. Reset Daily Usage
```sql
-- รีเซ็ตยอดใช้รายวัน (เที่ยงคืน)
UPDATE bank_accounts 
SET current_daily_usage = 0.00
WHERE DATE(updated_at) < CURDATE();
```

#### 3. Backup Critical Data
```sql
-- สร้าง Backup สำหรับข้อมูลสำคัญ
CREATE TABLE backup_transactions_YYYYMMDD AS 
SELECT * FROM transactions WHERE DATE(created_at) = CURDATE();

CREATE TABLE backup_journal_entries_YYYYMMDD AS 
SELECT * FROM journal_entries WHERE entry_date = CURDATE();
```

### Weekly Maintenance

#### 1. Performance Analysis
```sql
-- ตรวจสอบ Query ที่ช้า
SELECT 
    DIGEST_TEXT,
    COUNT_STAR,
    AVG_TIMER_WAIT/********** as avg_time_seconds,
    SUM_TIMER_WAIT/********** as total_time_seconds
FROM performance_schema.events_statements_summary_by_digest 
WHERE DIGEST_TEXT LIKE '%transactions%'
ORDER BY AVG_TIMER_WAIT DESC
LIMIT 10;
```

#### 2. Index Usage Analysis
```sql
-- ตรวจสอบการใช้งาน Index
SELECT 
    t.TABLE_NAME,
    s.INDEX_NAME,
    s.CARDINALITY,
    s.SEQ_IN_INDEX,
    s.COLUMN_NAME
FROM information_schema.STATISTICS s
JOIN information_schema.TABLES t ON s.TABLE_NAME = t.TABLE_NAME
WHERE t.TABLE_SCHEMA = DATABASE()
AND t.TABLE_NAME IN ('transactions', 'journal_lines', 'bank_transactions')
ORDER BY t.TABLE_NAME, s.INDEX_NAME, s.SEQ_IN_INDEX;
```

### Monthly Maintenance

#### 1. Data Archiving
```sql
-- Archive ข้อมูลเก่า 3 เดือน
CALL archive_old_transactions(DATE_SUB(CURDATE(), INTERVAL 3 MONTH));
```

#### 2. Statistics Update
```sql
-- อัพเดต Table Statistics
ANALYZE TABLE bank_accounts, merchants, transactions, journal_entries, journal_lines;
```

#### 3. Account Balance Reconciliation
```sql
-- ตรวจสอบความถูกต้องของยอดคงเหลือ
SELECT 
    m.merchant_code,
    m.deposit_balance as system_balance,
    COALESCE(SUM(CASE WHEN jl.account_id = 2 AND jl.reference_id = m.id 
                 THEN jl.debit_amount - jl.credit_amount ELSE 0 END), 0) as calculated_balance,
    ABS(m.deposit_balance - COALESCE(SUM(CASE WHEN jl.account_id = 2 AND jl.reference_id = m.id 
                                    THEN jl.debit_amount - jl.credit_amount ELSE 0 END), 0)) as difference
FROM merchants m
LEFT JOIN journal_lines jl ON jl.reference_type = 'merchant'
LEFT JOIN journal_entries je ON jl.journal_entry_id = je.id AND je.status = 'POSTED'
GROUP BY m.id
HAVING difference > 0.01; -- หาความแตกต่าง > 1 สตางค์
```

---

## 📞 สรุป

Database Schema นี้ได้รับการออกแบบมาเพื่อรองรับ:

### ✅ **ความถูกต้อง**
- Double Entry Bookkeeping ป้องกันข้อผิดพลาด
- Foreign Key Constraints รักษาความสมบูรณ์ของข้อมูล
- Transaction Management ป้องกันการสูญหายของข้อมูล

### ✅ **ประสิทธิภาพ**
- Load Balancing กระจายภาระบัญชีธนาคาร
- Proper Indexing เพิ่มความเร็วในการ Query
- Views สำหรับ Reporting ที่ซับซ้อน

### ✅ **ความยืดหยุ่น**
- เพิ่ม-ลดบัญชีธนาคารได้ง่าย
- ตั้งค่า MDR ได้ต่อร้าน
- รองรับหลายประเภทธุรกรรม

### ✅ **ความปลอดภัย**
- Audit Trail ครบถ้วน
- Balance Checking ป้องกันการใช้เงินเกิน
- Daily/Monthly Limits ควบคุมความเสี่ยง

### ✅ **ความสามารถในการขยายตัว**
- รองรับร้านค้าหลายพันร้าน
- รองรับรายการหลายล้านรายการ
- Archive Strategy สำหรับข้อมูลเก่า

Schema นี้พร้อมใช้งานจริงในระบบ Production และสามารถปรับแต่งเพิ่มเติมตามความต้องการเฉพาะได้