-- =====================================================
-- Stored Procedures สำหรับระบบ Double Entry Bookkeeping
-- ไฟล์: double_entry_procedures.sql
-- วัตถุประสงค์: เก็บ Stored Procedures ที่ใช้ในระบบบัญชีคู่
-- =====================================================

-- Procedure: สร้างรายการฝากเงิน (Deposit Transaction)
DELIMITER //
CREATE PROCEDURE สร้างรายการฝากเงิน(
    IN รหัสธุรกรรม BIGINT,
    IN รหัสร้านค้า BIGINT,
    IN รหัสบัญชีธนาคาร BIGINT,
    IN ยอดเงินรวม DECIMAL(15,2),
    IN ค่าธรรมเนียม DECIMAL(15,2),
    IN ยอดเงินสุทธิ DECIMAL(15,2),
    IN รายละเอียด TEXT,
    IN ผู้สร้าง BIGINT
)
COMMENT 'สร้างรายการบัญชีสำหรับธุรกรรมฝากเงิน (Double Entry)'
BEGIN
    DECLARE รหัสรายการบัญชี BIGINT;
    DECLARE เลขที่รายการบัญชี VARCHAR(100);
    DECLARE รหัสบัญชีเงินสดธนาคาร BIGINT;
    DECLARE รหัสบัญชียอดฝากร้านค้า BIGINT;
    DECLARE รหัสบัญชีรายได้ค่าธรรมเนียม BIGINT;
    DECLARE รหัสบัญชีเจ้าหนี้ร้านค้า BIGINT;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- สร้างเลขที่รายการบัญชี
    SET เลขที่รายการบัญชี = สร้างเลขที่รายการบัญชี();
    
    -- ดึงรหัสบัญชีที่ต้องใช้
    SELECT id INTO รหัสบัญชีเงินสดธนาคาร FROM accounts WHERE account_code = '1100';
    SELECT id INTO รหัสบัญชียอดฝากร้านค้า FROM accounts WHERE account_code = '1200';
    SELECT id INTO รหัสบัญชีรายได้ค่าธรรมเนียม FROM accounts WHERE account_code = '4100';
    SELECT id INTO รหัสบัญชีเจ้าหนี้ร้านค้า FROM accounts WHERE account_code = '2100';
    
    -- สร้างรายการบัญชีหลัก
    INSERT INTO journal_entries (journal_ref, transaction_id, entry_date, description, total_amount, status, created_by)
    VALUES (เลขที่รายการบัญชี, รหัสธุรกรรม, CURDATE(), รายละเอียด, ยอดเงินรวม, 'POSTED', ผู้สร้าง);
    
    SET รหัสรายการบัญชี = LAST_INSERT_ID();
    
    -- สร้างรายการย่อย (Double Entry) - ต้องมี Debit = Credit
    -- 1. Dr. เงินสดในธนาคาร (เงินเข้า)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชีเงินสดธนาคาร, ยอดเงินรวม, 0.00, 'เงินเข้าบัญชีธนาคาร', 'bank_account', รหัสบัญชีธนาคาร, 1);
    
    -- 2. Dr. ยอดเงินฝากของร้านค้า (ยอดสุทธิ)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชียอดฝากร้านค้า, ยอดเงินสุทธิ, 0.00, 'ยอดสุทธิเข้าร้านค้า', 'merchant', รหัสร้านค้า, 2);
    
    -- 3. Dr. รายได้ค่าธรรมเนียม MDR
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชีรายได้ค่าธรรมเนียม, ค่าธรรมเนียม, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', NULL, 3);
    
    -- 4. Cr. เจ้าหนี้ร้านค้า (หนี้สินที่ต้องจ่ายให้ร้านค้า)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชีเจ้าหนี้ร้านค้า, 0.00, ยอดเงินรวม, 'หนี้สินต่อร้านค้า', 'merchant', รหัสร้านค้า, 4);
    
    -- ตรวจสอบความสมดุล
    IF NOT ตรวจสอบความสมดุล(รหัสรายการบัญชี) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'รายการบัญชีไม่สมดุล: เดบิต ≠ เครดิต';
    END IF;
    
    -- อัพเดตยอดคงเหลือในตาราง accounts
    UPDATE accounts SET current_balance = ดึงยอดคงเหลือ(id, CURDATE()) 
    WHERE id IN (รหัสบัญชีเงินสดธนาคาร, รหัสบัญชียอดฝากร้านค้า, รหัสบัญชีรายได้ค่าธรรมเนียม, รหัสบัญชีเจ้าหนี้ร้านค้า);
    
    COMMIT;
    
    -- ส่งคืนเลขที่รายการบัญชีที่สร้าง
    SELECT เลขที่รายการบัญชี AS 'เลขที่รายการบัญชี', รหัสรายการบัญชี AS 'รหัสรายการบัญชี';
END //
DELIMITER ;

-- Procedure: สร้างรายการโอนเงิน ระหว่าง Deposit กับ Withdraw Balance
DELIMITER //
CREATE PROCEDURE สร้างรายการโอนเงิน(
    IN รหัสธุรกรรม BIGINT,
    IN รหัสร้านค้า BIGINT,
    IN จำนวนเงิน DECIMAL(15,2),
    IN รายละเอียด TEXT,
    IN ผู้สร้าง BIGINT
)
COMMENT 'สร้างรายการบัญชีสำหรับโอนเงินระหว่าง Deposit และ Withdraw Balance'
BEGIN
    DECLARE รหัสรายการบัญชี BIGINT;
    DECLARE เลขที่รายการบัญชี VARCHAR(100);
    DECLARE รหัสบัญชียอดฝาก BIGINT;
    DECLARE รหัสบัญชียอดถอน BIGINT;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- สร้างเลขที่รายการบัญชี
    SET เลขที่รายการบัญชี = สร้างเลขที่รายการบัญชี();

    -- ดึงรหัสบัญชี
    SELECT id INTO รหัสบัญชียอดฝาก FROM accounts WHERE account_code = '1200';
    SELECT id INTO รหัสบัญชียอดถอน FROM accounts WHERE account_code = '1300';

    -- สร้างรายการบัญชีหลัก
    INSERT INTO journal_entries (journal_ref, transaction_id, entry_date, description, total_amount, status, created_by)
    VALUES (เลขที่รายการบัญชี, รหัสธุรกรรม, CURDATE(), รายละเอียด, จำนวนเงิน, 'POSTED', ผู้สร้าง);

    SET รหัสรายการบัญชี = LAST_INSERT_ID();

    -- สร้างรายการย่อย (Double Entry)
    -- 1. Cr. ยอดเงินฝาก (ลดยอดฝาก)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชียอดฝาก, 0.00, จำนวนเงิน, 'โอนออกจากยอดฝาก', 'merchant', รหัสร้านค้า, 1);

    -- 2. Dr. ยอดเงินถอน (เพิ่มยอดถอน)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชียอดถอน, จำนวนเงิน, 0.00, 'โอนเข้ายอดถอน', 'merchant', รหัสร้านค้า, 2);

    -- ตรวจสอบความสมดุล
    IF NOT ตรวจสอบความสมดุล(รหัสรายการบัญชี) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'รายการบัญชีไม่สมดุล: เดบิต ≠ เครดิต';
    END IF;

    -- อัพเดตยอดคงเหลือ
    UPDATE accounts SET current_balance = ดึงยอดคงเหลือ(id, CURDATE())
    WHERE id IN (รหัสบัญชียอดฝาก, รหัสบัญชียอดถอน);

    COMMIT;

    -- ส่งคืนเลขที่รายการบัญชีที่สร้าง
    SELECT เลขที่รายการบัญชี AS 'เลขที่รายการบัญชี', รหัสรายการบัญชี AS 'รหัสรายการบัญชี';
END //
DELIMITER ;

-- Procedure: สร้าง Snapshot ยอดคงเหลือรายวัน
DELIMITER //
CREATE PROCEDURE สร้างสแนปช็อตรายวัน(
    IN วันที่สแนปช็อต DATE
)
COMMENT 'สร้างสแนปช็อตยอดคงเหลือของทุกบัญชีสำหรับวันที่ระบุ'
BEGIN
    DECLARE เสร็จแล้ว INT DEFAULT FALSE;
    DECLARE รหัสบัญชี BIGINT;
    DECLARE ยอดยกมา DECIMAL(15,2);
    DECLARE ยอดยกไป DECIMAL(15,2);
    DECLARE เดบิตรวม DECIMAL(15,2);
    DECLARE เครดิตรวม DECIMAL(15,2);

    DECLARE cursor_บัญชี CURSOR FOR
        SELECT id FROM accounts WHERE is_active = TRUE;

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET เสร็จแล้ว = TRUE;

    START TRANSACTION;

    -- ลบ snapshot เก่าถ้ามี
    DELETE FROM balance_snapshots WHERE snapshot_date = วันที่สแนปช็อต;

    OPEN cursor_บัญชี;

    วนลูป: LOOP
        FETCH cursor_บัญชี INTO รหัสบัญชี;
        IF เสร็จแล้ว THEN
            LEAVE วนลูป;
        END IF;

        -- คำนวนยอดยกมา (วันก่อนหน้า)
        SET ยอดยกมา = ดึงยอดคงเหลือ(รหัสบัญชี, DATE_SUB(วันที่สแนปช็อต, INTERVAL 1 DAY));

        -- คำนวนยอดรวมวันนี้
        SELECT
            COALESCE(SUM(jl.debit_amount), 0),
            COALESCE(SUM(jl.credit_amount), 0)
        INTO เดบิตรวม, เครดิตรวม
        FROM journal_lines jl
        JOIN journal_entries je ON jl.journal_entry_id = je.id
        WHERE jl.account_id = รหัสบัญชี
        AND je.status = 'POSTED'
        AND je.entry_date = วันที่สแนปช็อต;

        -- คำนวนยอดยกไป
        SET ยอดยกไป = ดึงยอดคงเหลือ(รหัสบัญชี, วันที่สแนปช็อต);

        -- บันทึก Snapshot
        INSERT INTO balance_snapshots (snapshot_date, account_id, opening_balance, debit_total, credit_total, closing_balance)
        VALUES (วันที่สแนปช็อต, รหัสบัญชี, ยอดยกมา, เดบิตรวม, เครดิตรวม, ยอดยกไป);

    END LOOP;

    CLOSE cursor_บัญชี;

    COMMIT;

    -- ส่งคืนจำนวนบัญชีที่ทำ snapshot
    SELECT COUNT(*) AS 'จำนวนบัญชีที่ทำ_snapshot' FROM balance_snapshots WHERE snapshot_date = วันที่สแนปช็อต;
END //
DELIMITER ;

-- Procedure: ตรวจสอบความถูกต้องของระบบ Double Entry
DELIMITER //
CREATE PROCEDURE ตรวจสอบความถูกต้อง(
    IN วันที่ตรวจสอบ DATE,
    OUT ถูกต้อง BOOLEAN,
    OUT ข้อความผิดพลาด TEXT
)
COMMENT 'ตรวจสอบความถูกต้องของระบบ Double Entry (Trial Balance และ Journal Balance)'
BEGIN
    DECLARE เดบิตรวม DECIMAL(15,2) DEFAULT 0.00;
    DECLARE เครดิตรวม DECIMAL(15,2) DEFAULT 0.00;
    DECLARE ผลต่าง DECIMAL(15,2) DEFAULT 0.00;
    DECLARE รายการไม่สมดุล INT DEFAULT 0;

    SET ถูกต้อง = TRUE;
    SET ข้อความผิดพลาด = '';

    -- ตรวจสอบ 1: Trial Balance ต้องสมดุล (เดบิตรวม = เครดิตรวม)
    SELECT
        COALESCE(SUM(jl.debit_amount), 0),
        COALESCE(SUM(jl.credit_amount), 0)
    INTO เดบิตรวม, เครดิตรวม
    FROM journal_lines jl
    JOIN journal_entries je ON jl.journal_entry_id = je.id
    WHERE je.status = 'POSTED'
    AND (วันที่ตรวจสอบ IS NULL OR je.entry_date <= วันที่ตรวจสอบ);

    SET ผลต่าง = เดบิตรวม - เครดิตรวม;

    IF ABS(ผลต่าง) > 0.01 THEN
        SET ถูกต้อง = FALSE;
        SET ข้อความผิดพลาด = CONCAT('Trial Balance ไม่สมดุล: เดบิต=', เดบิตรวม, ', เครดิต=', เครดิตรวม, ', ผลต่าง=', ผลต่าง);
    END IF;

    -- ตรวจสอบ 2: รายการบัญชีแต่ละรายการต้องสมดุล
    IF ถูกต้อง = TRUE THEN
        SELECT COUNT(*) INTO รายการไม่สมดุล
        FROM (
            SELECT
                je.id,
                SUM(jl.debit_amount) - SUM(jl.credit_amount) as difference
            FROM journal_entries je
            JOIN journal_lines jl ON je.id = jl.journal_entry_id
            WHERE je.status = 'POSTED'
            AND (วันที่ตรวจสอบ IS NULL OR je.entry_date <= วันที่ตรวจสอบ)
            GROUP BY je.id
            HAVING ABS(difference) > 0.01
        ) as unbalanced;

        IF รายการไม่สมดุล > 0 THEN
            SET ถูกต้อง = FALSE;
            SET ข้อความผิดพลาด = CONCAT('มีรายการบัญชีที่ไม่สมดุล จำนวน ', รายการไม่สมดุล, ' รายการ');
        END IF;
    END IF;

    -- ถ้าทุกอย่างถูกต้อง
    IF ถูกต้อง = TRUE THEN
        SET ข้อความผิดพลาด = CONCAT('ระบบ Double Entry ถูกต้อง - เดบิตรวม: ', เดบิตรวม, ', เครดิตรวม: ', เครดิตรวม);
    END IF;

END //
DELIMITER ;

-- =====================================================
-- ตัวอย่างการใช้งาน Stored Procedures
-- =====================================================

-- ตัวอย่าง: สร้างรายการฝากเงิน
-- CALL สร้างรายการฝากเงิน(1, 1, 1, 1000.00, 15.00, 985.00, 'ลูกค้าฝากเงิน 1000 บาท', 1);

-- ตัวอย่าง: สร้างรายการถอนเงิน
-- CALL สร้างรายการถอนเงิน(2, 1, 1, 500.00, 10.00, 490.00, 'ลูกค้าถอนเงิน 500 บาท', 1);

-- ตัวอย่าง: โอนเงินระหว่าง Deposit และ Withdraw
-- CALL สร้างรายการโอนเงิน(3, 1, 200.00, 'โอนเงินจาก Deposit ไป Withdraw', 1);

-- ตัวอย่าง: สร้าง Snapshot รายวัน
-- CALL สร้างสแนปช็อตรายวัน(CURDATE());

-- ตัวอย่าง: ตรวจสอบความถูกต้องของระบบ
-- CALL ตรวจสอบความถูกต้อง(CURDATE(), @ถูกต้อง, @ข้อความ);
-- SELECT @ถูกต้อง AS 'ระบบถูกต้อง', @ข้อความ AS 'ข้อความ';

-- Procedure: สร้างรายการฝากเงินพร้อม Bank Transaction
DELIMITER //
CREATE PROCEDURE สร้างรายการฝากเงินพร้อมธนาคาร(
    IN รหัสธุรกรรม BIGINT,
    IN รหัสร้านค้า BIGINT,
    IN รหัสบัญชีธนาคาร BIGINT,
    IN ยอดเงินรวม DECIMAL(15,2),
    IN ค่าธรรมเนียม DECIMAL(15,2),
    IN ยอดเงินสุทธิ DECIMAL(15,2),
    IN รายละเอียด TEXT,
    IN เลขที่อ้างอิงธนาคาร VARCHAR(100),
    IN ผู้สร้าง BIGINT
)
COMMENT 'สร้างรายการฝากเงินพร้อมบันทึกธุรกรรมธนาคารและ Double Entry'
BEGIN
    DECLARE รหัสรายการบัญชี BIGINT;
    DECLARE เลขที่รายการบัญชี VARCHAR(100);
    DECLARE ยอดคงเหลือใหม่ DECIMAL(15,2);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- ตรวจสอบธุรกรรมซ้ำ
    IF ตรวจสอบธุรกรรมซ้ำ(รหัสบัญชีธนาคาร, เลขที่อ้างอิงธนาคาร, NOW()) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'ธุรกรรมธนาคารซ้ำ: มีรายการนี้อยู่แล้ว';
    END IF;

    -- คำนวนยอดคงเหลือใหม่
    SET ยอดคงเหลือใหม่ = ดึงยอดคงเหลือธนาคาร(รหัสบัญชีธนาคาร) + ยอดเงินรวม;

    -- บันทึกธุรกรรมธนาคาร
    INSERT INTO bank_transactions (
        bank_account_id, transaction_ref, transaction_date, transaction_type,
        amount, balance_after, description, channel, status, related_transaction_id
    ) VALUES (
        รหัสบัญชีธนาคาร, เลขที่อ้างอิงธนาคาร, NOW(), 'DEPOSIT',
        ยอดเงินรวม, ยอดคงเหลือใหม่, รายละเอียด, 'API', 'SUCCESS', รหัสธุรกรรม
    );

    -- สร้างรายการ Double Entry
    CALL สร้างรายการฝากเงิน(
        รหัสธุรกรรม, รหัสร้านค้า, รหัสบัญชีธนาคาร,
        ยอดเงินรวม, ค่าธรรมเนียม, ยอดเงินสุทธิ, รายละเอียด, ผู้สร้าง
    );

    COMMIT;

    -- ส่งคืนข้อมูลสรุป
    SELECT
        เลขที่อ้างอิงธนาคาร AS 'เลขที่อ้างอิงธนาคาร',
        ยอดคงเหลือใหม่ AS 'ยอดคงเหลือธนาคาร',
        'สำเร็จ' AS 'สถานะ';
END //
DELIMITER ;

-- Procedure: สร้างรายการถอนเงินพร้อม Bank Transaction
DELIMITER //
CREATE PROCEDURE สร้างรายการถอนเงินพร้อมธนาคาร(
    IN รหัสธุรกรรม BIGINT,
    IN รหัสร้านค้า BIGINT,
    IN รหัสบัญชีธนาคาร BIGINT,
    IN ยอดเงินรวม DECIMAL(15,2),
    IN ค่าธรรมเนียม DECIMAL(15,2),
    IN ยอดเงินสุทธิ DECIMAL(15,2),
    IN รายละเอียด TEXT,
    IN เลขที่อ้างอิงธนาคาร VARCHAR(100),
    IN ผู้สร้าง BIGINT
)
COMMENT 'สร้างรายการถอนเงินพร้อมบันทึกธุรกรรมธนาคารและ Double Entry'
BEGIN
    DECLARE รหัสรายการบัญชี BIGINT;
    DECLARE เลขที่รายการบัญชี VARCHAR(100);
    DECLARE ยอดคงเหลือใหม่ DECIMAL(15,2);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- ตรวจสอบธุรกรรมซ้ำ
    IF ตรวจสอบธุรกรรมซ้ำ(รหัสบัญชีธนาคาร, เลขที่อ้างอิงธนาคาร, NOW()) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'ธุรกรรมธนาคารซ้ำ: มีรายการนี้อยู่แล้ว';
    END IF;

    -- คำนวนยอดคงเหลือใหม่
    SET ยอดคงเหลือใหม่ = ดึงยอดคงเหลือธนาคาร(รหัสบัญชีธนาคาร) - ยอดเงินรวม;

    -- ตรวจสอบยอดคงเหลือเพียงพอ
    IF ยอดคงเหลือใหม่ < 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'ยอดคงเหลือไม่เพียงพอสำหรับการถอนเงิน';
    END IF;

    -- บันทึกธุรกรรมธนาคาร
    INSERT INTO bank_transactions (
        bank_account_id, transaction_ref, transaction_date, transaction_type,
        amount, balance_after, description, channel, status, related_transaction_id
    ) VALUES (
        รหัสบัญชีธนาคาร, เลขที่อ้างอิงธนาคาร, NOW(), 'WITHDRAW',
        ยอดเงินรวม, ยอดคงเหลือใหม่, รายละเอียด, 'API', 'SUCCESS', รหัสธุรกรรม
    );

    -- สร้างรายการ Double Entry
    CALL สร้างรายการถอนเงิน(
        รหัสธุรกรรม, รหัสร้านค้า, รหัสบัญชีธนาคาร,
        ยอดเงินรวม, ค่าธรรมเนียม, ยอดเงินสุทธิ, รายละเอียด, ผู้สร้าง
    );

    COMMIT;

    -- ส่งคืนข้อมูลสรุป
    SELECT
        เลขที่อ้างอิงธนาคาร AS 'เลขที่อ้างอิงธนาคาร',
        ยอดคงเหลือใหม่ AS 'ยอดคงเหลือธนาคาร',
        'สำเร็จ' AS 'สถานะ';
END //
DELIMITER ;

-- Procedure: สร้างรายการถอนเงิน (Withdraw Transaction)
DELIMITER //
CREATE PROCEDURE สร้างรายการถอนเงิน(
    IN รหัสธุรกรรม BIGINT,
    IN รหัสร้านค้า BIGINT,
    IN รหัสบัญชีธนาคาร BIGINT,
    IN ยอดเงินรวม DECIMAL(15,2),
    IN ค่าธรรมเนียม DECIMAL(15,2),
    IN ยอดเงินสุทธิ DECIMAL(15,2),
    IN รายละเอียด TEXT,
    IN ผู้สร้าง BIGINT
)
COMMENT 'สร้างรายการบัญชีสำหรับธุรกรรมถอนเงิน (Double Entry)'
BEGIN
    DECLARE รหัสรายการบัญชี BIGINT;
    DECLARE เลขที่รายการบัญชี VARCHAR(100);
    DECLARE รหัสบัญชีเงินสดธนาคาร BIGINT;
    DECLARE รหัสบัญชียอดถอนร้านค้า BIGINT;
    DECLARE รหัสบัญชีรายได้ค่าธรรมเนียม BIGINT;
    DECLARE รหัสบัญชีเจ้าหนี้ร้านค้า BIGINT;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- สร้างเลขที่รายการบัญชี
    SET เลขที่รายการบัญชี = สร้างเลขที่รายการบัญชี();
    
    -- ดึงรหัสบัญชีที่ต้องใช้
    SELECT id INTO รหัสบัญชีเงินสดธนาคาร FROM accounts WHERE account_code = '1100';
    SELECT id INTO รหัสบัญชียอดถอนร้านค้า FROM accounts WHERE account_code = '1300';
    SELECT id INTO รหัสบัญชีรายได้ค่าธรรมเนียม FROM accounts WHERE account_code = '4200';
    SELECT id INTO รหัสบัญชีเจ้าหนี้ร้านค้า FROM accounts WHERE account_code = '2100';
    
    -- สร้างรายการบัญชีหลัก
    INSERT INTO journal_entries (journal_ref, transaction_id, entry_date, description, total_amount, status, created_by)
    VALUES (เลขที่รายการบัญชี, รหัสธุรกรรม, CURDATE(), รายละเอียด, ยอดเงินรวม, 'POSTED', ผู้สร้าง);
    
    SET รหัสรายการบัญชี = LAST_INSERT_ID();
    
    -- สร้างรายการย่อย (Double Entry) - ต้องมี Debit = Credit
    -- 1. Cr. เงินสดในธนาคาร (เงินออก)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชีเงินสดธนาคาร, 0.00, ยอดเงินรวม, 'เงินออกจากบัญชีธนาคาร', 'bank_account', รหัสบัญชีธนาคาร, 1);
    
    -- 2. Cr. ยอดเงินถอนของร้านค้า (ยอดสุทธิ)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชียอดถอนร้านค้า, 0.00, ยอดเงินสุทธิ, 'ยอดสุทธิออกจากร้านค้า', 'merchant', รหัสร้านค้า, 2);
    
    -- 3. Dr. รายได้ค่าธรรมเนียม MDR
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชีรายได้ค่าธรรมเนียม, ค่าธรรมเนียม, 0.00, 'รายได้ค่าธรรมเนียม MDR', 'system', NULL, 3);
    
    -- 4. Dr. เจ้าหนี้ร้านค้า (ลดหนี้สินที่ต้องจ่ายให้ร้านค้า)
    INSERT INTO journal_lines (journal_entry_id, account_id, debit_amount, credit_amount, description, reference_type, reference_id, line_number)
    VALUES (รหัสรายการบัญชี, รหัสบัญชีเจ้าหนี้ร้านค้า, ยอดเงินรวม, 0.00, 'ลดหนี้สินต่อร้านค้า', 'merchant', รหัสร้านค้า, 4);
    
    -- ตรวจสอบความสมดุล
    IF NOT ตรวจสอบความสมดุล(รหัสรายการบัญชี) THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'รายการบัญชีไม่สมดุล: เดบิต ≠ เครดิต';
    END IF;
    
    -- อัพเดตยอดคงเหลือในตาราง accounts
    UPDATE accounts SET current_balance = ดึงยอดคงเหลือ(id, CURDATE()) 
    WHERE id IN (รหัสบัญชีเงินสดธนาคาร, รหัสบัญชียอดถอนร้านค้า, รหัสบัญชีรายได้ค่าธรรมเนียม, รหัสบัญชีเจ้าหนี้ร้านค้า);
    
    COMMIT;
    
    -- ส่งคืนเลขที่รายการบัญชีที่สร้าง
    SELECT เลขที่รายการบัญชี AS 'เลขที่รายการบัญชี', รหัสรายการบัญชี AS 'รหัสรายการบัญชี';
END //
DELIMITER ;
