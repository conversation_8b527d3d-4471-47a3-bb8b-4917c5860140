
-- =====================================================
-- Views สำหรับระบบ Double Entry (รายงานบัญชี)
-- =====================================================

-- View: Trial Balance - งบทดลอง (ตรวจสอบความสมดุล)
CREATE VIEW view_trial_balance AS
SELECT
    a.account_code AS 'รหัสบัญชี',
    a.account_name AS 'ชื่อบัญชี',
    a.account_type AS 'ประเภทบัญชี',
    a.normal_balance AS 'ยอดปกติ',
    COALESCE(SUM(jl.debit_amount), 0) AS 'ยอดเดบิตรวม',
    COALESCE(SUM(jl.credit_amount), 0) AS 'ยอดเครดิตรวม',
    CASE
        WHEN a.normal_balance = 'DEBIT' THEN COALESCE(SUM(jl.debit_amount), 0) - COALESCE(SUM(jl.credit_amount), 0)
        ELSE COALESCE(SUM(jl.credit_amount), 0) - COALESCE(SUM(jl.debit_amount), 0)
    END AS 'ยอดคงเหลือ',
    a.is_active AS 'สถานะใช้งาน'
FROM accounts a
LEFT JOIN journal_lines jl ON a.id = jl.account_id
LEFT JOIN journal_entries je ON jl.journal_entry_id = je.id AND je.status = 'POSTED'
WHERE a.is_active = TRUE
GROUP BY a.id
ORDER BY a.account_code;

-- View: Balance Sheet - งบดุล (สินทรัพย์ และ หนี้สิน)
CREATE VIEW view_balance_sheet AS
SELECT
    a.account_type AS 'ประเภทบัญชี',
    a.account_code AS 'รหัสบัญชี',
    a.account_name AS 'ชื่อบัญชี',
    CASE
        WHEN a.normal_balance = 'DEBIT' THEN COALESCE(SUM(jl.debit_amount), 0) - COALESCE(SUM(jl.credit_amount), 0)
        ELSE COALESCE(SUM(jl.credit_amount), 0) - COALESCE(SUM(jl.debit_amount), 0)
    END AS 'ยอดคงเหลือ'
FROM accounts a
LEFT JOIN journal_lines jl ON a.id = jl.account_id
LEFT JOIN journal_entries je ON jl.journal_entry_id = je.id AND je.status = 'POSTED'
WHERE a.account_type IN ('ASSET', 'LIABILITY') AND a.is_active = TRUE
GROUP BY a.id
HAVING ยอดคงเหลือ != 0
ORDER BY a.account_type, a.account_code;

-- View: Income Statement - งบกำไรขาดทุน (รายได้ และ ค่าใช้จ่าย)
CREATE VIEW view_income_statement AS
SELECT
    a.account_type AS 'ประเภทบัญชี',
    a.account_code AS 'รหัสบัญชี',
    a.account_name AS 'ชื่อบัญชี',
    CASE
        WHEN a.normal_balance = 'DEBIT' THEN COALESCE(SUM(jl.debit_amount), 0) - COALESCE(SUM(jl.credit_amount), 0)
        ELSE COALESCE(SUM(jl.credit_amount), 0) - COALESCE(SUM(jl.debit_amount), 0)
    END AS 'ยอดคงเหลือ'
FROM accounts a
LEFT JOIN journal_lines jl ON a.id = jl.account_id
LEFT JOIN journal_entries je ON jl.journal_entry_id = je.id AND je.status = 'POSTED'
WHERE a.account_type IN ('REVENUE', 'EXPENSE') AND a.is_active = TRUE
GROUP BY a.id
HAVING ยอดคงเหลือ != 0
ORDER BY a.account_type DESC, a.account_code;

-- View: Journal Entry Details - รายละเอียดรายการบัญชี
CREATE VIEW view_journal_entry_details AS
SELECT
    je.journal_ref AS 'เลขที่รายการ',
    je.entry_date AS 'วันที่',
    je.description AS 'รายละเอียดหลัก',
    je.total_amount AS 'ยอดรวม',
    je.status AS 'สถานะ',
    jl.line_number AS 'บรรทัดที่',
    a.account_code AS 'รหัสบัญชี',
    a.account_name AS 'ชื่อบัญชี',
    jl.debit_amount AS 'เดบิต',
    jl.credit_amount AS 'เครดิต',
    jl.description AS 'รายละเอียดย่อย',
    jl.reference_type AS 'ประเภทอ้างอิง',
    jl.reference_id AS 'รหัสอ้างอิง',
    CASE
        WHEN jl.reference_type = 'merchant' THEN m.merchant_name
        WHEN jl.reference_type = 'bank_account' THEN ba.bank_name
        WHEN jl.reference_type = 'user' THEN CONCAT(u.first_name, ' ', u.last_name)
        ELSE 'ระบบ'
    END AS 'ชื่อผู้เกี่ยวข้อง'
FROM journal_entries je
JOIN journal_lines jl ON je.id = jl.journal_entry_id
JOIN accounts a ON jl.account_id = a.id
LEFT JOIN merchants m ON jl.reference_type = 'merchant' AND jl.reference_id = m.id
LEFT JOIN bank_accounts ba ON jl.reference_type = 'bank_account' AND jl.reference_id = ba.id
LEFT JOIN users u ON jl.reference_type = 'user' AND jl.reference_id = u.id
ORDER BY je.entry_date DESC, je.journal_ref, jl.line_number;

-- View: Daily Balance Summary - สรุปยอดรายวัน
CREATE VIEW view_daily_balance_summary AS
SELECT
    DATE(je.entry_date) AS 'วันที่',
    a.account_type AS 'ประเภทบัญชี',
    a.account_code AS 'รหัสบัญชี',
    a.account_name AS 'ชื่อบัญชี',
    COALESCE(SUM(jl.debit_amount), 0) AS 'เดบิตรวม',
    COALESCE(SUM(jl.credit_amount), 0) AS 'เครดิตรวม',
    CASE
        WHEN a.normal_balance = 'DEBIT' THEN COALESCE(SUM(jl.debit_amount), 0) - COALESCE(SUM(jl.credit_amount), 0)
        ELSE COALESCE(SUM(jl.credit_amount), 0) - COALESCE(SUM(jl.debit_amount), 0)
    END AS 'การเปลี่ยนแปลงสุทธิ'
FROM journal_entries je
JOIN journal_lines jl ON je.id = jl.journal_entry_id
JOIN accounts a ON jl.account_id = a.id
WHERE je.status = 'POSTED'
GROUP BY DATE(je.entry_date), a.id
ORDER BY วันที่ DESC, a.account_code;

-- View: Bank Transaction Summary - สรุปธุรกรรมธนาคาร
CREATE VIEW view_bank_transaction_summary AS
SELECT
    ba.bank_name AS 'ธนาคาร',
    ba.account_number AS 'เลขที่บัญชี',
    bt.transaction_date AS 'วันที่ธุรกรรม',
    bt.transaction_type AS 'ประเภท',
    bt.amount AS 'จำนวนเงิน',
    bt.balance_after AS 'ยอดคงเหลือ',
    bt.description AS 'รายละเอียด',
    bt.channel AS 'ช่องทาง',
    bt.status AS 'สถานะ',
    bt.fee_amount AS 'ค่าธรรมเนียม',
    CASE
        WHEN bt.related_transaction_id IS NOT NULL THEN CONCAT('TXN-', bt.related_transaction_id)
        ELSE 'ไม่เกี่ยวข้อง'
    END AS 'ธุรกรรมที่เกี่ยวข้อง'
FROM bank_transactions bt
JOIN bank_accounts ba ON bt.bank_account_id = ba.id
ORDER BY bt.transaction_date DESC, bt.id DESC;

-- View: Daily Bank Balance - ยอดคงเหลือธนาคารรายวัน
CREATE VIEW view_daily_bank_balance AS
SELECT
    ba.bank_name AS 'ธนาคาร',
    ba.account_number AS 'เลขที่บัญชี',
    DATE(bt.transaction_date) AS 'วันที่',
    MIN(bt.balance_after) AS 'ยอดต่ำสุด',
    MAX(bt.balance_after) AS 'ยอดสูงสุด',
    (SELECT bt2.balance_after
     FROM bank_transactions bt2
     WHERE bt2.bank_account_id = ba.id
     AND DATE(bt2.transaction_date) = DATE(bt.transaction_date)
     ORDER BY bt2.transaction_date DESC, bt2.id DESC
     LIMIT 1) AS 'ยอดปิด',
    COUNT(*) AS 'จำนวนธุรกรรม',
    SUM(CASE WHEN bt.transaction_type IN ('DEPOSIT', 'TRANSFER_IN') THEN bt.amount ELSE 0 END) AS 'เงินเข้ารวม',
    SUM(CASE WHEN bt.transaction_type IN ('WITHDRAW', 'TRANSFER_OUT', 'FEE') THEN bt.amount ELSE 0 END) AS 'เงินออกรวม'
FROM bank_transactions bt
JOIN bank_accounts ba ON bt.bank_account_id = ba.id
WHERE bt.status = 'SUCCESS'
GROUP BY ba.id, DATE(bt.transaction_date)
ORDER BY วันที่ DESC, ba.bank_name;

-- =====================================================
-- หมายเหตุสำหรับระบบ Double Entry
-- =====================================================

-- ไฟล์นี้ประกอบด้วย:
-- 1. ตารางหลัก (Tables) สำหรับระบบ Double Entry
-- 2. Views สำหรับรายงานบัญชี
-- 3. ข้อมูลผังบัญชีเริ่มต้น

-- สำหรับ Functions และ Stored Procedures ได้แยกออกไปเป็นไฟล์ต่างหาก:
-- - double_entry_functions.sql   : Functions สำหรับคำนวณและตรวจสอบ
-- - double_entry_procedures.sql  : Stored Procedures สำหรับสร้างรายการ
-- - README_double_entry.md       : เอกสารการใช้งาน

-- วิธีการติดตั้ง:
-- 1. SOURCE database_schema.sql;          (ไฟล์นี้)
-- 2. SOURCE double_entry_functions.sql;   (Functions)
-- 3. SOURCE double_entry_procedures.sql;  (Procedures)

-- ตัวอย่างการใช้งาน:
-- CALL สร้างรายการฝากเงิน(1, 1, 1, 1000.00, 15.00, 985.00, 'ฝากเงิน', 1);
-- SELECT * FROM view_trial_balance;
-- CALL ตรวจสอบความถูกต้อง(CURDATE(), @ok, @msg);


-- =====================================================
-- Functions สำหรับระบบ Double Entry Bookkeeping
-- ไฟล์: double_entry_functions.sql
-- วัตถุประสงค์: เก็บ Functions ที่ใช้ในระบบบัญชีคู่
-- =====================================================

-- Function: คำนวนค่าธรรมเนียม MDR
DELIMITER //
CREATE FUNCTION คำนวนค่าธรรมเนียม(
    รหัสร้านค้า BIGINT,
    ประเภทธุรกรรม VARCHAR(20),
    จำนวนเงิน DECIMAL(15,2)
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
COMMENT 'คำนวนค่าธรรมเนียม MDR ตามประเภทธุรกรรมและร้านค้า'
BEGIN
    DECLARE ค่าธรรมเนียม DECIMAL(15,2) DEFAULT 0.00;
    DECLARE อัตราเปอร์เซ็นต์ DECIMAL(5,2);
    DECLARE ค่าธรรมเนียมคงที่ DECIMAL(10,2);
    
    -- ดึงอัตราค่าธรรมเนียมจากตารางร้านค้า
    SELECT 
        CASE ประเภทธุรกรรม
            WHEN 'DEPOSIT' THEN deposit_mdr_rate
            WHEN 'TOPUP' THEN deposit_mdr_rate
            ELSE 0.00
        END,
        CASE ประเภทธุรกรรม
            WHEN 'WITHDRAW' THEN withdraw_mdr_fixed
            WHEN 'SETTLEMENT' THEN settlement_mdr_fixed
            ELSE 0.00
        END
    INTO อัตราเปอร์เซ็นต์, ค่าธรรมเนียมคงที่
    FROM merchants 
    WHERE id = รหัสร้านค้า;
    
    -- คำนวนค่าธรรมเนียม
    IF ประเภทธุรกรรม IN ('DEPOSIT', 'TOPUP') THEN
        -- ใช้อัตราเปอร์เซ็นต์สำหรับฝาก/เติมเงิน
        SET ค่าธรรมเนียม = จำนวนเงิน * อัตราเปอร์เซ็นต์ / 100;
    ELSEIF ประเภทธุรกรรม IN ('WITHDRAW', 'SETTLEMENT') THEN
        -- ใช้ค่าธรรมเนียมคงที่สำหรับถอน/Settlement
        SET ค่าธรรมเนียม = ค่าธรรมเนียมคงที่;
    END IF;
    
    RETURN ค่าธรรมเนียม;
END //
DELIMITER ;

-- Function: ตรวจสอบความสมดุลของรายการบัญชี
DELIMITER //
CREATE FUNCTION ตรวจสอบความสมดุล(
    รหัสรายการบัญชี BIGINT
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
COMMENT 'ตรวจสอบว่ารายการบัญชีมี Debit = Credit หรือไม่'
BEGIN
    DECLARE ยอดเดบิตรวม DECIMAL(15,2) DEFAULT 0.00;
    DECLARE ยอดเครดิตรวม DECIMAL(15,2) DEFAULT 0.00;
    DECLARE สมดุล BOOLEAN DEFAULT FALSE;
    
    -- คำนวนยอดรวม Debit และ Credit ของรายการบัญชี
    SELECT 
        COALESCE(SUM(debit_amount), 0),
        COALESCE(SUM(credit_amount), 0)
    INTO ยอดเดบิตรวม, ยอดเครดิตรวม
    FROM journal_lines 
    WHERE journal_entry_id = รหัสรายการบัญชี;
    
    -- ตรวจสอบความสมดุล (Debit = Credit)
    IF ยอดเดบิตรวม = ยอดเครดิตรวม THEN
        SET สมดุล = TRUE;
    END IF;
    
    RETURN สมดุล;
END //
DELIMITER ;

-- Function: ดึงยอดคงเหลือของบัญชี ณ วันที่ระบุ
DELIMITER //
CREATE FUNCTION ดึงยอดคงเหลือ(
    รหัสบัญชี BIGINT,
    ณ_วันที่ DATE
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
COMMENT 'ดึงยอดคงเหลือของบัญชี ณ วันที่ระบุ (คำนวนตามประเภทบัญชี)'
BEGIN
    DECLARE ยอดคงเหลือ DECIMAL(15,2) DEFAULT 0.00;
    DECLARE ยอดปกติ VARCHAR(10);
    DECLARE ยอดเดบิตรวม DECIMAL(15,2) DEFAULT 0.00;
    DECLARE ยอดเครดิตรวม DECIMAL(15,2) DEFAULT 0.00;
    
    -- ดึงประเภทยอดปกติของบัญชี (DEBIT หรือ CREDIT)
    SELECT normal_balance INTO ยอดปกติ
    FROM accounts 
    WHERE id = รหัสบัญชี;
    
    -- คำนวนยอดรวม Debit และ Credit ตั้งแต่เริ่มต้นจนถึงวันที่ระบุ
    SELECT 
        COALESCE(SUM(jl.debit_amount), 0),
        COALESCE(SUM(jl.credit_amount), 0)
    INTO ยอดเดบิตรวม, ยอดเครดิตรวม
    FROM journal_lines jl
    JOIN journal_entries je ON jl.journal_entry_id = je.id
    WHERE jl.account_id = รหัสบัญชี 
    AND je.status = 'POSTED'
    AND je.entry_date <= ณ_วันที่;
    
    -- คำนวนยอดคงเหลือตามประเภทบัญชี
    IF ยอดปกติ = 'DEBIT' THEN
        -- บัญชีประเภท DEBIT (สินทรัพย์, ค่าใช้จ่าย): Debit - Credit
        SET ยอดคงเหลือ = ยอดเดบิตรวม - ยอดเครดิตรวม;
    ELSE
        -- บัญชีประเภท CREDIT (หนี้สิน, รายได้): Credit - Debit
        SET ยอดคงเหลือ = ยอดเครดิตรวม - ยอดเดบิตรวม;
    END IF;
    
    RETURN ยอดคงเหลือ;
END //
DELIMITER ;

-- Function: ดึงยอดคงเหลือของร้านค้า (Deposit Balance)
DELIMITER //
CREATE FUNCTION ดึงยอดฝากร้านค้า(
    รหัสร้านค้า BIGINT,
    ณ_วันที่ DATE
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
COMMENT 'ดึงยอดเงินฝากของร้านค้า ณ วันที่ระบุ'
BEGIN
    DECLARE ยอดฝาก DECIMAL(15,2) DEFAULT 0.00;
    DECLARE รหัสบัญชียอดฝาก BIGINT;
    
    -- ดึงรหัสบัญชียอดฝากร้านค้า
    SELECT id INTO รหัสบัญชียอดฝาก 
    FROM accounts 
    WHERE account_code = '1200';
    
    -- คำนวนยอดฝากของร้านค้าเฉพาะ
    SELECT COALESCE(SUM(
        CASE 
            WHEN jl.reference_type = 'merchant' AND jl.reference_id = รหัสร้านค้า 
            THEN jl.debit_amount - jl.credit_amount
            ELSE 0
        END
    ), 0) INTO ยอดฝาก
    FROM journal_lines jl
    JOIN journal_entries je ON jl.journal_entry_id = je.id
    WHERE jl.account_id = รหัสบัญชียอดฝาก
    AND je.status = 'POSTED'
    AND je.entry_date <= ณ_วันที่;
    
    RETURN ยอดฝาก;
END //
DELIMITER ;

-- Function: ดึงยอดคงเหลือของร้านค้า (Withdraw Balance)
DELIMITER //
CREATE FUNCTION ดึงยอดถอนร้านค้า(
    รหัสร้านค้า BIGINT,
    ณ_วันที่ DATE
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
COMMENT 'ดึงยอดเงินถอนของร้านค้า ณ วันที่ระบุ'
BEGIN
    DECLARE ยอดถอน DECIMAL(15,2) DEFAULT 0.00;
    DECLARE รหัสบัญชียอดถอน BIGINT;
    
    -- ดึงรหัสบัญชียอดถอนร้านค้า
    SELECT id INTO รหัสบัญชียอดถอน 
    FROM accounts 
    WHERE account_code = '1300';
    
    -- คำนวนยอดถอนของร้านค้าเฉพาะ
    SELECT COALESCE(SUM(
        CASE 
            WHEN jl.reference_type = 'merchant' AND jl.reference_id = รหัสร้านค้า 
            THEN jl.debit_amount - jl.credit_amount
            ELSE 0
        END
    ), 0) INTO ยอดถอน
    FROM journal_lines jl
    JOIN journal_entries je ON jl.journal_entry_id = je.id
    WHERE jl.account_id = รหัสบัญชียอดถอน
    AND je.status = 'POSTED'
    AND je.entry_date <= ณ_วันที่;
    
    RETURN ยอดถอน;
END //
DELIMITER ;

-- Function: สร้างเลขที่รายการบัญชีอัตโนมัติ
DELIMITER //
CREATE FUNCTION สร้างเลขที่รายการบัญชี() 
RETURNS VARCHAR(100)
DETERMINISTIC
COMMENT 'สร้างเลขที่รายการบัญชีแบบอัตโนมัติ รูปแบบ JE + วันที่เวลา'
BEGIN
    DECLARE เลขที่ VARCHAR(100);
    
    -- สร้างเลขที่รูปแบบ JE + YYYYMMDDHHMMSS + เลขสุ่ม 3 หลัก
    SET เลขที่ = CONCAT(
        'JE',
        DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'),
        LPAD(FLOOR(RAND() * 1000), 3, '0')
    );
    
    RETURN เลขที่;
END //
DELIMITER ;

-- =====================================================
-- ตัวอย่างการใช้งาน Functions
-- =====================================================

-- ตัวอย่าง: คำนวนค่าธรรมเนียม
-- SELECT คำนวนค่าธรรมเนียม(1, 'DEPOSIT', 1000.00) AS 'ค่าธรรมเนียม';

-- ตัวอย่าง: ดึงยอดคงเหลือบัญชี
-- SELECT ดึงยอดคงเหลือ(1, CURDATE()) AS 'ยอดคงเหลือ';

-- ตัวอย่าง: ดึงยอดฝากร้านค้า
-- SELECT ดึงยอดฝากร้านค้า(1, CURDATE()) AS 'ยอดฝากร้านค้า';

-- ตัวอย่าง: สร้างเลขที่รายการบัญชี
-- SELECT สร้างเลขที่รายการบัญชี() AS 'เลขที่รายการใหม่';

-- Function: ดึงยอดคงเหลือธนาคารล่าสุด
DELIMITER //
CREATE FUNCTION ดึงยอดคงเหลือธนาคาร(
    รหัสบัญชีธนาคาร BIGINT
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
COMMENT 'ดึงยอดคงเหลือล่าสุดของบัญชีธนาคาร'
BEGIN
    DECLARE ยอดคงเหลือ DECIMAL(15,2) DEFAULT 0.00;

    -- ดึงยอดคงเหลือจากธุรกรรมล่าสุด
    SELECT COALESCE(balance_after, 0.00) INTO ยอดคงเหลือ
    FROM bank_transactions
    WHERE bank_account_id = รหัสบัญชีธนาคาร
    AND status = 'SUCCESS'
    ORDER BY transaction_date DESC, id DESC
    LIMIT 1;

    RETURN ยอดคงเหลือ;
END //
DELIMITER ;

-- Function: ตรวจสอบธุรกรรมธนาคารซ้ำ
DELIMITER //
CREATE FUNCTION ตรวจสอบธุรกรรมซ้ำ(
    รหัสบัญชีธนาคาร BIGINT,
    เลขที่อ้างอิง VARCHAR(100),
    วันที่ธุรกรรม DATETIME
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
COMMENT 'ตรวจสอบว่ามีธุรกรรมซ้ำในระบบหรือไม่'
BEGIN
    DECLARE จำนวน INT DEFAULT 0;

    -- นับจำนวนธุรกรรมที่ซ้ำ
    SELECT COUNT(*) INTO จำนวน
    FROM bank_transactions
    WHERE bank_account_id = รหัสบัญชีธนาคาร
    AND transaction_ref = เลขที่อ้างอิง
    AND transaction_date = วันที่ธุรกรรม;

    RETURN จำนวน > 0;
END //
DELIMITER ;

-- Function: คำนวนยอดเงินเข้ารายวัน
DELIMITER //
CREATE FUNCTION คำนวนเงินเข้ารายวัน(
    รหัสบัญชีธนาคาร BIGINT,
    วันที่ DATE
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
COMMENT 'คำนวนยอดเงินเข้ารวมในวันที่ระบุ'
BEGIN
    DECLARE ยอดเงินเข้า DECIMAL(15,2) DEFAULT 0.00;

    -- รวมยอดเงินเข้าในวันที่ระบุ
    SELECT COALESCE(SUM(amount), 0.00) INTO ยอดเงินเข้า
    FROM bank_transactions
    WHERE bank_account_id = รหัสบัญชีธนาคาร
    AND DATE(transaction_date) = วันที่
    AND transaction_type IN ('DEPOSIT', 'TRANSFER_IN')
    AND status = 'SUCCESS';

    RETURN ยอดเงินเข้า;
END //
DELIMITER ;

-- Function: คำนวนยอดเงินออกรายวัน
DELIMITER //
CREATE FUNCTION คำนวนเงินออกรายวัน(
    รหัสบัญชีธนาคาร BIGINT,
    วันที่ DATE
) RETURNS DECIMAL(15,2)
READS SQL DATA
DETERMINISTIC
COMMENT 'คำนวนยอดเงินออกรวมในวันที่ระบุ'
BEGIN
    DECLARE ยอดเงินออก DECIMAL(15,2) DEFAULT 0.00;

    -- รวมยอดเงินออกในวันที่ระบุ
    SELECT COALESCE(SUM(amount), 0.00) INTO ยอดเงินออก
    FROM bank_transactions
    WHERE bank_account_id = รหัสบัญชีธนาคาร
    AND DATE(transaction_date) = วันที่
    AND transaction_type IN ('WITHDRAW', 'TRANSFER_OUT', 'FEE')
    AND status = 'SUCCESS';

    RETURN ยอดเงินออก;
END //
DELIMITER ;
