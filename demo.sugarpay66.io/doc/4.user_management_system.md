# ระบบจัดการผู้ใช้งาน (User Management System)
## SugarPay Payment System

---

## 📋 ภาพรวมระบบ

ระบบจัดการผู้ใช้งานของ SugarPay ประกอบด้วย 4 ส่วนหลัก:
1. **Users** - ข้อมูลผู้ใช้งานพื้นฐาน
2. **User Groups** - กลุ่มผู้ใช้งานตามบทบาท
3. **Permissions** - สิทธิ์การเข้าถึงระบบ
4. **User Permissions** - การกำหนดสิทธิ์ให้ผู้ใช้

---

## 🗄️ Database Schema

### 1. ตาราง Users (ผู้ใช้งาน)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON>HAR(50) NOT NULL,
    phone VARCHAR(20),
    avatar_url VARCHAR(255),
    
    -- ข้อมูลสถานะ
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMP NULL,
    last_login_at TIMESTAMP NULL,
    
    -- 2FA Google Authenticator
    google_2fa_secret VARCHAR(255) NULL,
    google_2fa_enabled BOOLEAN DEFAULT FALSE,
    
    -- ข้อมูลการสร้างและแก้ไข
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    updated_by INT NULL,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

### 2. ตาราง User Groups (กลุ่มผู้ใช้งาน)
```sql
CREATE TABLE user_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_code VARCHAR(20) NOT NULL UNIQUE,
    group_name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- ระดับสิทธิ์
    level INT DEFAULT 1, -- 1=ต่ำสุด, 10=สูงสุด
    
    -- สถานะ
    is_active BOOLEAN DEFAULT TRUE,
    is_system_group BOOLEAN DEFAULT FALSE, -- กลุ่มระบบที่ไม่สามารถลบได้
    
    -- ข้อมูลการสร้างและแก้ไข
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    updated_by INT NULL,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

### 3. ตาราง Permissions (สิทธิ์การเข้าถึง)
```sql
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    permission_code VARCHAR(50) NOT NULL UNIQUE,
    permission_name VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- หมวดหมู่สิทธิ์
    module VARCHAR(50) NOT NULL, -- dashboard, merchants, transactions, reports, settings
    action VARCHAR(20) NOT NULL, -- view, create, edit, delete, approve
    
    -- สถานะ
    is_active BOOLEAN DEFAULT TRUE,
    is_system_permission BOOLEAN DEFAULT FALSE,
    
    -- ข้อมูลการสร้างและแก้ไข
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    updated_by INT NULL,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

### 4. ตาราง User Group Members (สมาชิกในกลุ่ม)
```sql
CREATE TABLE user_group_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    group_id INT NOT NULL,
    
    -- สถานะ
    is_active BOOLEAN DEFAULT TRUE,
    
    -- ข้อมูลการเข้าร่วม
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP NULL,
    assigned_by INT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    
    UNIQUE KEY unique_user_group (user_id, group_id)
);
```

### 5. ตาราง Group Permissions (สิทธิ์ของกลุ่ม)
```sql
CREATE TABLE group_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    permission_id INT NOT NULL,
    
    -- สถานะ
    is_granted BOOLEAN DEFAULT TRUE,
    
    -- ข้อมูลการกำหนดสิทธิ์
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by INT NULL,
    
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    
    UNIQUE KEY unique_group_permission (group_id, permission_id)
);
```

### 6. ตาราง User Permissions (สิทธิ์เฉพาะบุคคล)
```sql
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    permission_id INT NOT NULL,
    
    -- ประเภทการกำหนดสิทธิ์
    permission_type ENUM('GRANT', 'DENY') DEFAULT 'GRANT',
    
    -- สถานะ
    is_active BOOLEAN DEFAULT TRUE,
    
    -- ข้อมูลการกำหนดสิทธิ์
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL, -- วันหมดอายุสิทธิ์ (ถ้ามี)
    granted_by INT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    
    UNIQUE KEY unique_user_permission (user_id, permission_id)
);
```

---

## 📊 ข้อมูลเริ่มต้น (Initial Data)

### กลุ่มผู้ใช้งานพื้นฐาน
```sql
INSERT INTO user_groups (id, group_code, group_name, description, level, is_system_group) VALUES
(1, 'SUPER_ADMIN', 'ผู้ดูแลระบบสูงสุด', 'มีสิทธิ์เต็มในการจัดการระบบทั้งหมด', 10, TRUE),
(2, 'ADMIN', 'ผู้ดูแลระบบ', 'จัดการระบบและผู้ใช้งานทั่วไป', 8, TRUE),
(3, 'MANAGER', 'ผู้จัดการ', 'จัดการข้อมูลร้านค้าและรายงาน', 6, TRUE),
(4, 'OPERATOR', 'พนักงานปฏิบัติการ', 'ดูแลรายการธุรกรรมและการอนุมัติ', 4, TRUE),
(5, 'VIEWER', 'ผู้ดูข้อมูล', 'ดูข้อมูลและรายงานเท่านั้น', 2, TRUE),
(6, 'MERCHANT', 'ร้านค้า', 'เข้าถึงระบบร้านค้าเท่านั้น', 1, TRUE);
```

### สิทธิ์การเข้าถึงพื้นฐาน
```sql
INSERT INTO permissions (id, permission_code, permission_name, module, action, is_system_permission) VALUES
-- Dashboard
(1, 'dashboard.view', 'ดูหน้าแดชบอร์ด', 'dashboard', 'view', TRUE),
(2, 'dashboard.stats', 'ดูสถิติระบบ', 'dashboard', 'view', TRUE),

-- User Management
(3, 'users.view', 'ดูรายการผู้ใช้งาน', 'users', 'view', TRUE),
(4, 'users.create', 'สร้างผู้ใช้งานใหม่', 'users', 'create', TRUE),
(5, 'users.edit', 'แก้ไขข้อมูลผู้ใช้งาน', 'users', 'edit', TRUE),
(6, 'users.delete', 'ลบผู้ใช้งาน', 'users', 'delete', TRUE),
(7, 'users.permissions', 'จัดการสิทธิ์ผู้ใช้งาน', 'users', 'edit', TRUE),

-- Merchant Management
(8, 'merchants.view', 'ดูรายการร้านค้า', 'merchants', 'view', TRUE),
(9, 'merchants.create', 'สร้างร้านค้าใหม่', 'merchants', 'create', TRUE),
(10, 'merchants.edit', 'แก้ไขข้อมูลร้านค้า', 'merchants', 'edit', TRUE),
(11, 'merchants.delete', 'ลบร้านค้า', 'merchants', 'delete', TRUE),
(12, 'merchants.balance', 'ดูยอดเงินร้านค้า', 'merchants', 'view', TRUE),

-- Transaction Management
(13, 'transactions.view', 'ดูรายการธุรกรรม', 'transactions', 'view', TRUE),
(14, 'transactions.approve', 'อนุมัติรายการธุรกรรม', 'transactions', 'approve', TRUE),
(15, 'transactions.cancel', 'ยกเลิกรายการธุรกรรม', 'transactions', 'edit', TRUE),
(16, 'transactions.refund', 'คืนเงินรายการธุรกรรม', 'transactions', 'edit', TRUE),

-- Bank Management
(17, 'banks.view', 'ดูข้อมูลบัญชีธนาคาร', 'banks', 'view', TRUE),
(18, 'banks.create', 'เพิ่มบัญชีธนาคาร', 'banks', 'create', TRUE),
(19, 'banks.edit', 'แก้ไขข้อมูลบัญชีธนาคาร', 'banks', 'edit', TRUE),
(20, 'banks.delete', 'ลบบัญชีธนาคาร', 'banks', 'delete', TRUE),

-- Reports
(21, 'reports.view', 'ดูรายงาน', 'reports', 'view', TRUE),
(22, 'reports.export', 'ส่งออกรายงาน', 'reports', 'view', TRUE),
(23, 'reports.financial', 'ดูรายงานการเงิน', 'reports', 'view', TRUE),

-- System Settings
(24, 'settings.view', 'ดูการตั้งค่าระบบ', 'settings', 'view', TRUE),
(25, 'settings.edit', 'แก้ไขการตั้งค่าระบบ', 'settings', 'edit', TRUE),
(26, 'settings.backup', 'สำรองข้อมูลระบบ', 'settings', 'edit', TRUE);

### การกำหนดสิทธิ์ให้กลุ่มผู้ใช้งาน
```sql
-- SUPER_ADMIN: มีสิทธิ์ทั้งหมด
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 1, id, 1 FROM permissions WHERE is_active = TRUE;

-- ADMIN: มีสิทธิ์เกือบทั้งหมด ยกเว้นการสำรองข้อมูล
INSERT INTO group_permissions (group_id, permission_id, granted_by) VALUES
(2, 1, 1), (2, 2, 1), (2, 3, 1), (2, 4, 1), (2, 5, 1), (2, 6, 1), (2, 7, 1),
(2, 8, 1), (2, 9, 1), (2, 10, 1), (2, 11, 1), (2, 12, 1),
(2, 13, 1), (2, 14, 1), (2, 15, 1), (2, 16, 1),
(2, 17, 1), (2, 18, 1), (2, 19, 1), (2, 20, 1),
(2, 21, 1), (2, 22, 1), (2, 23, 1),
(2, 24, 1), (2, 25, 1);

-- MANAGER: จัดการร้านค้า รายงาน และดูธุรกรรม
INSERT INTO group_permissions (group_id, permission_id, granted_by) VALUES
(3, 1, 1), (3, 2, 1),
(3, 8, 1), (3, 9, 1), (3, 10, 1), (3, 12, 1),
(3, 13, 1), (3, 14, 1),
(3, 17, 1),
(3, 21, 1), (3, 22, 1), (3, 23, 1);

-- OPERATOR: ปฏิบัติการธุรกรรมและดูข้อมูลพื้นฐาน
INSERT INTO group_permissions (group_id, permission_id, granted_by) VALUES
(4, 1, 1),
(4, 8, 1), (4, 12, 1),
(4, 13, 1), (4, 14, 1), (4, 15, 1),
(4, 17, 1),
(4, 21, 1);

-- VIEWER: ดูข้อมูลเท่านั้น
INSERT INTO group_permissions (group_id, permission_id, granted_by) VALUES
(5, 1, 1),
(5, 8, 1), (5, 12, 1),
(5, 13, 1),
(5, 17, 1),
(5, 21, 1);

-- MERCHANT: สิทธิ์พื้นฐานสำหรับร้านค้า
INSERT INTO group_permissions (group_id, permission_id, granted_by) VALUES
(6, 1, 1),
(6, 12, 1),
(6, 13, 1);
```

---

## 👥 ตัวอย่างการสร้างผู้ใช้งาน

### ผู้ดูแลระบบ
```sql
-- สร้างผู้ใช้งาน Super Admin
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at) VALUES
(1, 'superadmin', '<EMAIL>', '$2y$10$example_hash_here', 'Super', 'Administrator', '02-000-0001', TRUE, TRUE, NOW());

-- เพิ่มเข้ากลุ่ม SUPER_ADMIN
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(1, 1, 1);

-- สร้างผู้ใช้งาน Admin
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, created_by) VALUES
(2, 'admin', '<EMAIL>', '$2y$10$example_hash_here', 'System', 'Admin', '02-000-0002', TRUE, TRUE, NOW(), 1);

-- เพิ่มเข้ากลุ่ม ADMIN
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(2, 2, 1);
```

### ผู้จัดการ
```sql
-- สร้างผู้ใช้งาน Manager
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, created_by) VALUES
(3, 'manager01', '<EMAIL>', '$2y$10$example_hash_here', 'สมชาย', 'ใจดี', '02-111-1111', TRUE, TRUE, NOW(), 1),
(4, 'manager02', '<EMAIL>', '$2y$10$example_hash_here', 'สมหญิง', 'รักงาน', '02-222-2222', TRUE, TRUE, NOW(), 1);

-- เพิ่มเข้ากลุ่ม MANAGER
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(3, 3, 1),
(4, 3, 1);
```

### พนักงานปฏิบัติการ
```sql
-- สร้างผู้ใช้งาน Operator
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, created_by) VALUES
(5, 'operator01', '<EMAIL>', '$2y$10$example_hash_here', 'สมศักดิ์', 'ขยันทำงาน', '02-333-3333', TRUE, TRUE, NOW(), 2),
(6, 'operator02', '<EMAIL>', '$2y$10$example_hash_here', 'สมปอง', 'ใส่ใจ', '02-444-4444', TRUE, TRUE, NOW(), 2),
(7, 'operator03', '<EMAIL>', '$2y$10$example_hash_here', 'สมใจ', 'รับผิดชอบ', '02-555-5555', TRUE, TRUE, NOW(), 2);

-- เพิ่มเข้ากลุ่ม OPERATOR
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(5, 4, 2),
(6, 4, 2),
(7, 4, 2);
```

### ผู้ดูข้อมูล
```sql
-- สร้างผู้ใช้งาน Viewer
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, created_by) VALUES
(8, 'viewer01', '<EMAIL>', '$2y$10$example_hash_here', 'สมคิด', 'วิเคราะห์', '02-666-6666', TRUE, TRUE, NOW(), 3),
(9, 'viewer02', '<EMAIL>', '$2y$10$example_hash_here', 'สมรู้', 'ติดตาม', '02-777-7777', TRUE, TRUE, NOW(), 3);

-- เพิ่มเข้ากลุ่ม VIEWER
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(8, 5, 3),
(9, 5, 3);
```

---

## 🔐 ตัวอย่างการกำหนดสิทธิ์เฉพาะบุคคล

### กำหนดสิทธิ์พิเศษ
```sql
-- ให้ operator01 สิทธิ์พิเศษในการคืนเงิน
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(5, 16, 'GRANT', 2); -- transactions.refund

-- ให้ viewer01 สิทธิ์ในการส่งออกรายงาน
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(8, 22, 'GRANT', 3); -- reports.export

-- ห้าม operator02 ลบรายการธุรกรรม (แม้กลุ่มจะมีสิทธิ์)
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(6, 15, 'DENY', 2); -- transactions.cancel
```

### กำหนดสิทธิ์ชั่วคราว
```sql
-- ให้ viewer02 สิทธิ์ดูรายงานการเงินเป็นเวลา 30 วัน
INSERT INTO user_permissions (user_id, permission_id, permission_type, expires_at, granted_by) VALUES
(9, 23, 'GRANT', DATE_ADD(NOW(), INTERVAL 30 DAY), 3); -- reports.financial
```

---

## 📊 Views สำหรับการจัดการ

### View: ดูสิทธิ์ของผู้ใช้งาน
```sql
CREATE VIEW view_user_permissions AS
SELECT
    u.id as user_id,
    u.username,
    u.first_name,
    u.last_name,
    ug.group_name,
    p.permission_code,
    p.permission_name,
    p.module,
    p.action,
    CASE
        WHEN up.permission_type = 'DENY' THEN 'ปฏิเสธ'
        WHEN up.permission_type = 'GRANT' OR gp.is_granted = TRUE THEN 'อนุญาต'
        ELSE 'ไม่มีสิทธิ์'
    END as permission_status,
    CASE
        WHEN up.id IS NOT NULL THEN 'เฉพาะบุคคล'
        WHEN gp.id IS NOT NULL THEN 'จากกลุ่ม'
        ELSE 'ไม่มี'
    END as permission_source,
    up.expires_at
FROM users u
LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.is_active = TRUE
LEFT JOIN user_groups ug ON ugm.group_id = ug.id AND ug.is_active = TRUE
LEFT JOIN group_permissions gp ON ug.id = gp.group_id AND gp.is_granted = TRUE
LEFT JOIN permissions p ON gp.permission_id = p.id OR p.id IN (
    SELECT permission_id FROM user_permissions WHERE user_id = u.id AND is_active = TRUE
)
LEFT JOIN user_permissions up ON u.id = up.user_id AND p.id = up.permission_id AND up.is_active = TRUE
WHERE u.is_active = TRUE AND p.is_active = TRUE
ORDER BY u.username, p.module, p.action;
```

### View: สรุปผู้ใช้งานและกลุ่ม
```sql
CREATE VIEW view_user_summary AS
SELECT
    u.id,
    u.username,
    u.email,
    CONCAT(u.first_name, ' ', u.last_name) as full_name,
    u.phone,
    u.is_active,
    u.is_verified,
    u.google_2fa_enabled,
    u.last_login_at,
    GROUP_CONCAT(ug.group_name ORDER BY ug.level DESC SEPARATOR ', ') as user_groups,
    MAX(ug.level) as highest_level,
    COUNT(DISTINCT ugm.group_id) as group_count,
    u.created_at
FROM users u
LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.is_active = TRUE
LEFT JOIN user_groups ug ON ugm.group_id = ug.id AND ug.is_active = TRUE
GROUP BY u.id
ORDER BY highest_level DESC, u.username;
```

---

## 🔍 Stored Procedures สำหรับการจัดการ

### ตรวจสอบสิทธิ์ผู้ใช้งาน
```sql
DELIMITER //
CREATE FUNCTION check_user_permission(
    p_user_id INT,
    p_permission_code VARCHAR(50)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_has_permission BOOLEAN DEFAULT FALSE;
    DECLARE v_is_denied BOOLEAN DEFAULT FALSE;

    -- ตรวจสอบว่ามีการปฏิเสธสิทธิ์เฉพาะบุคคลหรือไม่
    SELECT COUNT(*) > 0 INTO v_is_denied
    FROM user_permissions up
    JOIN permissions p ON up.permission_id = p.id
    WHERE up.user_id = p_user_id
    AND p.permission_code = p_permission_code
    AND up.permission_type = 'DENY'
    AND up.is_active = TRUE
    AND (up.expires_at IS NULL OR up.expires_at > NOW());

    -- ถ้าถูกปฏิเสธ ให้ return FALSE
    IF v_is_denied THEN
        RETURN FALSE;
    END IF;

    -- ตรวจสอบสิทธิ์เฉพาะบุคคล
    SELECT COUNT(*) > 0 INTO v_has_permission
    FROM user_permissions up
    JOIN permissions p ON up.permission_id = p.id
    WHERE up.user_id = p_user_id
    AND p.permission_code = p_permission_code
    AND up.permission_type = 'GRANT'
    AND up.is_active = TRUE
    AND (up.expires_at IS NULL OR up.expires_at > NOW());

    -- ถ้ามีสิทธิ์เฉพาะบุคคล ให้ return TRUE
    IF v_has_permission THEN
        RETURN TRUE;
    END IF;

    -- ตรวจสอบสิทธิ์จากกลุ่ม
    SELECT COUNT(*) > 0 INTO v_has_permission
    FROM user_group_members ugm
    JOIN group_permissions gp ON ugm.group_id = gp.group_id
    JOIN permissions p ON gp.permission_id = p.id
    WHERE ugm.user_id = p_user_id
    AND p.permission_code = p_permission_code
    AND ugm.is_active = TRUE
    AND gp.is_granted = TRUE;

    RETURN v_has_permission;
END //
DELIMITER ;
```

### เพิ่มผู้ใช้งานใหม่
```sql
DELIMITER //
CREATE PROCEDURE create_new_user(
    IN p_username VARCHAR(50),
    IN p_email VARCHAR(100),
    IN p_password_hash VARCHAR(255),
    IN p_first_name VARCHAR(50),
    IN p_last_name VARCHAR(50),
    IN p_phone VARCHAR(20),
    IN p_group_id INT,
    IN p_created_by INT
)
BEGIN
    DECLARE v_user_id INT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- สร้างผู้ใช้งาน
    INSERT INTO users (username, email, password_hash, first_name, last_name, phone, created_by)
    VALUES (p_username, p_email, p_password_hash, p_first_name, p_last_name, p_phone, p_created_by);

    SET v_user_id = LAST_INSERT_ID();

    -- เพิ่มเข้ากลุ่ม
    INSERT INTO user_group_members (user_id, group_id, assigned_by)
    VALUES (v_user_id, p_group_id, p_created_by);

    COMMIT;

    SELECT v_user_id as new_user_id;
END //
DELIMITER ;
```

---

## 📋 Indexes สำหรับ Performance

```sql
-- Users table indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_verified ON users(is_verified);

-- User Group Members indexes
CREATE INDEX idx_ugm_user ON user_group_members(user_id);
CREATE INDEX idx_ugm_group ON user_group_members(group_id);
CREATE INDEX idx_ugm_active ON user_group_members(is_active);

-- Permissions indexes
CREATE INDEX idx_permissions_code ON permissions(permission_code);
CREATE INDEX idx_permissions_module ON permissions(module);
CREATE INDEX idx_permissions_active ON permissions(is_active);

-- Group Permissions indexes
CREATE INDEX idx_gp_group ON group_permissions(group_id);
CREATE INDEX idx_gp_permission ON group_permissions(permission_id);

-- User Permissions indexes
CREATE INDEX idx_up_user ON user_permissions(user_id);
CREATE INDEX idx_up_permission ON user_permissions(permission_id);
CREATE INDEX idx_up_active ON user_permissions(is_active);
CREATE INDEX idx_up_expires ON user_permissions(expires_at);
```

---

## 🔧 ตัวอย่างการใช้งาน

### ตรวจสอบสิทธิ์ในระบบ
```sql
-- ตรวจสอบว่า operator01 มีสิทธิ์อนุมัติธุรกรรมหรือไม่
SELECT check_user_permission(5, 'transactions.approve') as can_approve;

-- ดูสิทธิ์ทั้งหมดของ manager01
SELECT * FROM view_user_permissions WHERE username = 'manager01';

-- ดูผู้ใช้งานที่มีสิทธิ์ลบร้านค้า
SELECT DISTINCT u.username, u.first_name, u.last_name
FROM view_user_permissions vup
JOIN users u ON vup.user_id = u.id
WHERE vup.permission_code = 'merchants.delete'
AND vup.permission_status = 'อนุญาต';
```

### การจัดการกลุ่มและสิทธิ์
```sql
-- เพิ่มสิทธิ์ใหม่ให้กลุ่ม MANAGER
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 3, id, 1 FROM permissions WHERE permission_code = 'banks.view';

-- ลบผู้ใช้งานออกจากกลุ่ม
UPDATE user_group_members
SET is_active = FALSE, left_at = NOW()
WHERE user_id = 6 AND group_id = 4;

-- เพิ่มผู้ใช้งานเข้ากลุ่มใหม่
INSERT INTO user_group_members (user_id, group_id, assigned_by)
VALUES (6, 5, 2); -- ย้าย operator02 ไปเป็น viewer
```

---

## 📝 หมายเหตุการใช้งาน

1. **ลำดับความสำคัญสิทธิ์:** DENY > GRANT (เฉพาะบุคคล) > กลุ่ม
2. **การหมดอายุ:** สิทธิ์เฉพาะบุคคลสามารถกำหนดวันหมดอายุได้
3. **2FA:** ระบบรองรับ Google Authenticator สำหรับความปลอดภัยเพิ่มเติม
4. **Audit Trail:** บันทึกผู้สร้างและผู้แก้ไขในทุกตาราง
5. **Soft Delete:** ใช้ is_active แทนการลบข้อมูลจริง

ระบบนี้ให้ความยืดหยุ่นในการจัดการสิทธิ์และรองรับการขยายตัวในอนาคต
```
