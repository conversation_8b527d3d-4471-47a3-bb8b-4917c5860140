# ระบบจัดการผู้ใช้งาน (User Management System)
## SugarPay Payment System

---

## 📋 ภาพรวมระบบ

ระบบจัดการผู้ใช้งานของ SugarPay แบบง่าย ประกอบด้วย 2 ตารางหลัก:
1. **Users** - ข้อมูลผู้ใช้งาน + กลุ่ม + สิทธิ์
2. **User Permissions** - สิทธิ์เฉพาะบุคคล (ถ้าจำเป็น)

---

## 🗄️ Database Schema

### 1. ตาราง Users (ผู้ใช้งาน) - รวมทุกอย่างในตารางเดียว
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),

    -- กลุ่มผู้ใช้งาน (แทน user_groups table)
    user_group ENUM('SUPER_ADMIN', 'ADMIN', 'MERCHANT') NOT NULL DEFAULT 'MERCHANT',

    -- ข้อมูลร้านค้า
    merchant_id INT NULL, -- ถ้าเป็น MERCHANT หรือ user ย่อย
    parent_user_id INT NULL, -- ถ้าเป็น user ย่อย จะระบุ parent (เจ้าของร้าน)

    -- สิทธิ์แบบ JSON (แทน permissions tables)
    permissions JSON NULL, -- เก็บสิทธิ์เป็น JSON array เช่น ["merchant.balance", "merchant.transactions"]

    -- สถานะ
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,

    -- 2FA
    google_2fa_secret VARCHAR(255) NULL,
    google_2fa_enabled BOOLEAN DEFAULT FALSE,

    -- ข้อมูลการสร้างและแก้ไข
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,

    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (parent_user_id) REFERENCES users(id)
);
```

### 2. ตาราง User Permissions (สิทธิ์เฉพาะบุคคล) - ใช้เฉพาะกรณีพิเศษ
```sql
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    permission_code VARCHAR(50) NOT NULL, -- เช่น "merchant.withdraw"
    permission_type ENUM('GRANT', 'DENY') DEFAULT 'GRANT',
    expires_at TIMESTAMP NULL, -- วันหมดอายุสิทธิ์
    granted_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE KEY unique_user_permission (user_id, permission_code)
);
```

---

## 📊 ข้อมูลเริ่มต้น (Initial Data)

### กลุ่มผู้ใช้งานพื้นฐาน
```sql
INSERT INTO user_groups (id, group_code, group_name, description, level, is_system_group) VALUES
(1, 'SUPER_ADMIN', 'ผู้ดูแลระบบสูงสุด', 'มีสิทธิ์เต็มในการจัดการระบบทั้งหมด', 10, TRUE),
(2, 'ADMIN', 'ผู้ดูแลระบบ', 'จัดการระบบและผู้ใช้งานทั่วไป', 8, TRUE),
(3, 'MERCHANT', 'ร้านค้า', 'เจ้าของร้าน สามารถสร้าง user ย่อยได้', 5, TRUE);
```

### สิทธิ์การเข้าถึงพื้นฐาน
```sql
INSERT INTO permissions (id, permission_code, permission_name, module, action, is_system_permission) VALUES
-- Dashboard
(1, 'dashboard.view', 'ดูหน้าแดชบอร์ด', 'dashboard', 'view', TRUE),
(2, 'dashboard.stats', 'ดูสถิติระบบ', 'dashboard', 'view', TRUE),

-- User Management (Backoffice)
(3, 'users.view', 'ดูรายการผู้ใช้งาน', 'users', 'view', TRUE),
(4, 'users.create', 'สร้างผู้ใช้งานใหม่', 'users', 'create', TRUE),
(5, 'users.edit', 'แก้ไขข้อมูลผู้ใช้งาน', 'users', 'edit', TRUE),
(6, 'users.delete', 'ลบผู้ใช้งาน', 'users', 'delete', TRUE),

-- Merchant Management (Backoffice)
(7, 'merchants.view', 'ดูรายการร้านค้า', 'merchants', 'view', TRUE),
(8, 'merchants.create', 'สร้างร้านค้าใหม่', 'merchants', 'create', TRUE),
(9, 'merchants.edit', 'แก้ไขข้อมูลร้านค้า', 'merchants', 'edit', TRUE),
(10, 'merchants.delete', 'ลบร้านค้า', 'merchants', 'delete', TRUE),

-- Transaction Management (Backoffice)
(11, 'transactions.view_all', 'ดูรายการธุรกรรมทั้งหมด', 'transactions', 'view', TRUE),
(12, 'transactions.approve', 'อนุมัติรายการธุรกรรม', 'transactions', 'approve', TRUE),
(13, 'transactions.cancel', 'ยกเลิกรายการธุรกรรม', 'transactions', 'edit', TRUE),
(14, 'transactions.refund', 'คืนเงินรายการธุรกรรม', 'transactions', 'edit', TRUE),

-- Bank Management (Backoffice)
(15, 'banks.view', 'ดูข้อมูลบัญชีธนาคาร', 'banks', 'view', TRUE),
(16, 'banks.create', 'เพิ่มบัญชีธนาคาร', 'banks', 'create', TRUE),
(17, 'banks.edit', 'แก้ไขข้อมูลบัญชีธนาคาร', 'banks', 'edit', TRUE),
(18, 'banks.delete', 'ลบบัญชีธนาคาร', 'banks', 'delete', TRUE),

-- Reports (Backoffice)
(19, 'reports.view_all', 'ดูรายงานทั้งหมด', 'reports', 'view', TRUE),
(20, 'reports.export', 'ส่งออกรายงาน', 'reports', 'view', TRUE),
(21, 'reports.financial', 'ดูรายงานการเงิน', 'reports', 'view', TRUE),

-- System Settings (Backoffice)
(22, 'settings.view', 'ดูการตั้งค่าระบบ', 'settings', 'view', TRUE),
(23, 'settings.edit', 'แก้ไขการตั้งค่าระบบ', 'settings', 'edit', TRUE),
(24, 'settings.backup', 'สำรองข้อมูลระบบ', 'settings', 'edit', TRUE),

-- Merchant System (สำหรับร้านค้า)
(25, 'merchant.dashboard', 'ดูแดชบอร์ดร้านค้า', 'merchant', 'view', TRUE),
(26, 'merchant.balance', 'ดูยอดเงินร้านตนเอง', 'merchant', 'view', TRUE),
(27, 'merchant.transactions', 'ดูรายการธุรกรรมร้านตนเอง', 'merchant', 'view', TRUE),
(28, 'merchant.withdraw', 'ถอนเงิน', 'merchant', 'create', TRUE),
(29, 'merchant.reports', 'ดูรายงานร้านตนเอง', 'merchant', 'view', TRUE),
(30, 'merchant.settings', 'ตั้งค่าร้านตนเอง', 'merchant', 'edit', TRUE),

-- User Sub Management (สำหรับเจ้าของร้าน)
(31, 'usersub.view', 'ดูรายการ User ย่อย', 'usersub', 'view', TRUE),
(32, 'usersub.create', 'สร้าง User ย่อย', 'usersub', 'create', TRUE),
(33, 'usersub.edit', 'แก้ไข User ย่อย', 'usersub', 'edit', TRUE),
(34, 'usersub.delete', 'ลบ User ย่อย', 'usersub', 'delete', TRUE),
(35, 'usersub.permissions', 'จัดการสิทธิ์ User ย่อย', 'usersub', 'edit', TRUE);

### การกำหนดสิทธิ์ให้กลุ่มผู้ใช้งาน
```sql
-- SUPER_ADMIN: มีสิทธิ์ทั้งหมด
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 1, id, 1 FROM permissions WHERE is_active = TRUE;

-- ADMIN: มีสิทธิ์ Backoffice ทั้งหมด ยกเว้นการสำรองข้อมูล
INSERT INTO group_permissions (group_id, permission_id, granted_by) VALUES
(2, 1, 1), (2, 2, 1), -- Dashboard
(2, 3, 1), (2, 4, 1), (2, 5, 1), (2, 6, 1), -- Users
(2, 7, 1), (2, 8, 1), (2, 9, 1), (2, 10, 1), -- Merchants
(2, 11, 1), (2, 12, 1), (2, 13, 1), (2, 14, 1), -- Transactions
(2, 15, 1), (2, 16, 1), (2, 17, 1), (2, 18, 1), -- Banks
(2, 19, 1), (2, 20, 1), (2, 21, 1), -- Reports
(2, 22, 1), (2, 23, 1); -- Settings (ไม่รวม backup)

-- MERCHANT: สิทธิ์ระบบร้านค้า + การจัดการ User ย่อย
INSERT INTO group_permissions (group_id, permission_id, granted_by) VALUES
(3, 25, 1), -- merchant.dashboard
(3, 26, 1), -- merchant.balance
(3, 27, 1), -- merchant.transactions
(3, 28, 1), -- merchant.withdraw
(3, 29, 1), -- merchant.reports
(3, 30, 1), -- merchant.settings
(3, 31, 1), -- usersub.view
(3, 32, 1), -- usersub.create
(3, 33, 1), -- usersub.edit
(3, 34, 1), -- usersub.delete
(3, 35, 1); -- usersub.permissions
```

---

## 👥 ตัวอย่างการสร้างผู้ใช้งาน

### ผู้ดูแลระบบ
```sql
-- สร้างผู้ใช้งาน Super Admin
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at) VALUES
(1, 'superadmin', '<EMAIL>', '$2y$10$example_hash_here', 'Super', 'Administrator', '02-000-0001', TRUE, TRUE, NOW());

-- เพิ่มเข้ากลุ่ม SUPER_ADMIN
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(1, 1, 1);

-- สร้างผู้ใช้งาน Admin
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, created_by) VALUES
(2, 'admin', '<EMAIL>', '$2y$10$example_hash_here', 'System', 'Admin', '02-000-0002', TRUE, TRUE, NOW(), 1);

-- เพิ่มเข้ากลุ่ม ADMIN
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(2, 2, 1);
```

### เจ้าของร้านค้า (MERCHANT)
```sql
-- สร้างเจ้าของร้าน "ร้านกาแฟดี"
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, merchant_id, created_by) VALUES
(3, 'coffee_owner', '<EMAIL>', '$2y$10$example_hash_here', 'สมศักดิ์', 'เจ้าของร้าน', '02-111-1111', TRUE, TRUE, NOW(), 1, 1);

-- เพิ่มเข้ากลุ่ม MERCHANT
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(3, 3, 1);

-- สร้างเจ้าของร้าน "ร้านเสื้อผ้าแฟชั่น"
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, merchant_id, created_by) VALUES
(4, 'fashion_owner', '<EMAIL>', '$2y$10$example_hash_here', 'สมหญิง', 'เจ้าของร้าน', '02-222-2222', TRUE, TRUE, NOW(), 2, 1);

-- เพิ่มเข้ากลุ่ม MERCHANT
INSERT INTO user_group_members (user_id, group_id, assigned_by) VALUES
(4, 3, 1);
```

### User ย่อย (สร้างโดยเจ้าของร้าน)
```sql
-- เจ้าของร้านกาแฟสร้าง User ย่อย (พนักงาน)
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, merchant_id, parent_user_id, created_by) VALUES
(5, 'coffee_staff01', '<EMAIL>', '$2y$10$example_hash_here', 'สมชาย', 'พนักงานร้าน', '02-333-3333', TRUE, TRUE, NOW(), 1, 3, 3),
(6, 'coffee_cashier', '<EMAIL>', '$2y$10$example_hash_here', 'สมใจ', 'แคชเชียร์', '02-444-4444', TRUE, TRUE, NOW(), 1, 3, 3);

-- เจ้าของร้านเสื้อผ้าสร้าง User ย่อย
INSERT INTO users (id, username, email, password_hash, first_name, last_name, phone, is_active, is_verified, email_verified_at, merchant_id, parent_user_id, created_by) VALUES
(7, 'fashion_manager', '<EMAIL>', '$2y$10$example_hash_here', 'สมปอง', 'ผู้จัดการร้าน', '02-555-5555', TRUE, TRUE, NOW(), 2, 4, 4),
(8, 'fashion_sales', '<EMAIL>', '$2y$10$example_hash_here', 'สมรัก', 'พนักงานขาย', '02-666-6666', TRUE, TRUE, NOW(), 2, 4, 4);
```

---

## 🔐 ตัวอย่างการกำหนดสิทธิ์เฉพาะบุคคล

### กำหนดสิทธิ์ให้ User ย่อย
```sql
-- เจ้าของร้านกาแฟให้สิทธิ์พนักงานดูยอดเงินและรายการธุรกรรม
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(5, 26, 'GRANT', 3), -- merchant.balance
(5, 27, 'GRANT', 3); -- merchant.transactions

-- เจ้าของร้านกาแฟให้สิทธิ์แคชเชียร์ดูยอดเงินเท่านั้น
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(6, 26, 'GRANT', 3); -- merchant.balance

-- เจ้าของร้านเสื้อผ้าให้สิทธิ์ผู้จัดการร้านเกือบทั้งหมด ยกเว้นการถอนเงิน
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(7, 25, 'GRANT', 4), -- merchant.dashboard
(7, 26, 'GRANT', 4), -- merchant.balance
(7, 27, 'GRANT', 4), -- merchant.transactions
(7, 29, 'GRANT', 4), -- merchant.reports
(7, 30, 'GRANT', 4); -- merchant.settings

-- เจ้าของร้านเสื้อผ้าให้สิทธิ์พนักงานขายดูข้อมูลพื้นฐาน
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(8, 25, 'GRANT', 4), -- merchant.dashboard
(8, 27, 'GRANT', 4); -- merchant.transactions
```

### กำหนดสิทธิ์ชั่วคราว
```sql
-- ให้พนักงานร้านกาแฟสิทธิ์ถอนเงินเป็นเวลา 7 วัน (เมื่อเจ้าของไม่อยู่)
INSERT INTO user_permissions (user_id, permission_id, permission_type, expires_at, granted_by) VALUES
(5, 28, 'GRANT', DATE_ADD(NOW(), INTERVAL 7 DAY), 3); -- merchant.withdraw

-- ห้ามพนักงานขายดูรายงาน (แม้จะได้รับสิทธิ์มาแล้ว)
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(8, 29, 'DENY', 4); -- merchant.reports
```

---

## 📊 Views สำหรับการจัดการ

### View: ดูสิทธิ์ของผู้ใช้งาน
```sql
CREATE VIEW view_user_permissions AS
SELECT
    u.id as user_id,
    u.username,
    u.first_name,
    u.last_name,
    ug.group_name,
    p.permission_code,
    p.permission_name,
    p.module,
    p.action,
    CASE
        WHEN up.permission_type = 'DENY' THEN 'ปฏิเสธ'
        WHEN up.permission_type = 'GRANT' OR gp.is_granted = TRUE THEN 'อนุญาต'
        ELSE 'ไม่มีสิทธิ์'
    END as permission_status,
    CASE
        WHEN up.id IS NOT NULL THEN 'เฉพาะบุคคล'
        WHEN gp.id IS NOT NULL THEN 'จากกลุ่ม'
        ELSE 'ไม่มี'
    END as permission_source,
    up.expires_at
FROM users u
LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.is_active = TRUE
LEFT JOIN user_groups ug ON ugm.group_id = ug.id AND ug.is_active = TRUE
LEFT JOIN group_permissions gp ON ug.id = gp.group_id AND gp.is_granted = TRUE
LEFT JOIN permissions p ON gp.permission_id = p.id OR p.id IN (
    SELECT permission_id FROM user_permissions WHERE user_id = u.id AND is_active = TRUE
)
LEFT JOIN user_permissions up ON u.id = up.user_id AND p.id = up.permission_id AND up.is_active = TRUE
WHERE u.is_active = TRUE AND p.is_active = TRUE
ORDER BY u.username, p.module, p.action;
```

### View: สรุปผู้ใช้งานและกลุ่ม
```sql
CREATE VIEW view_user_summary AS
SELECT
    u.id,
    u.username,
    u.email,
    CONCAT(u.first_name, ' ', u.last_name) as full_name,
    u.phone,
    u.is_active,
    u.is_verified,
    u.google_2fa_enabled,
    u.last_login_at,
    GROUP_CONCAT(ug.group_name ORDER BY ug.level DESC SEPARATOR ', ') as user_groups,
    MAX(ug.level) as highest_level,
    COUNT(DISTINCT ugm.group_id) as group_count,
    u.created_at
FROM users u
LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.is_active = TRUE
LEFT JOIN user_groups ug ON ugm.group_id = ug.id AND ug.is_active = TRUE
GROUP BY u.id
ORDER BY highest_level DESC, u.username;
```

---

## 🔍 Stored Procedures สำหรับการจัดการ

### ตรวจสอบสิทธิ์ผู้ใช้งาน
```sql
DELIMITER //
CREATE FUNCTION check_user_permission(
    p_user_id INT,
    p_permission_code VARCHAR(50)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_has_permission BOOLEAN DEFAULT FALSE;
    DECLARE v_is_denied BOOLEAN DEFAULT FALSE;

    -- ตรวจสอบว่ามีการปฏิเสธสิทธิ์เฉพาะบุคคลหรือไม่
    SELECT COUNT(*) > 0 INTO v_is_denied
    FROM user_permissions up
    JOIN permissions p ON up.permission_id = p.id
    WHERE up.user_id = p_user_id
    AND p.permission_code = p_permission_code
    AND up.permission_type = 'DENY'
    AND up.is_active = TRUE
    AND (up.expires_at IS NULL OR up.expires_at > NOW());

    -- ถ้าถูกปฏิเสธ ให้ return FALSE
    IF v_is_denied THEN
        RETURN FALSE;
    END IF;

    -- ตรวจสอบสิทธิ์เฉพาะบุคคล
    SELECT COUNT(*) > 0 INTO v_has_permission
    FROM user_permissions up
    JOIN permissions p ON up.permission_id = p.id
    WHERE up.user_id = p_user_id
    AND p.permission_code = p_permission_code
    AND up.permission_type = 'GRANT'
    AND up.is_active = TRUE
    AND (up.expires_at IS NULL OR up.expires_at > NOW());

    -- ถ้ามีสิทธิ์เฉพาะบุคคล ให้ return TRUE
    IF v_has_permission THEN
        RETURN TRUE;
    END IF;

    -- ตรวจสอบสิทธิ์จากกลุ่ม
    SELECT COUNT(*) > 0 INTO v_has_permission
    FROM user_group_members ugm
    JOIN group_permissions gp ON ugm.group_id = gp.group_id
    JOIN permissions p ON gp.permission_id = p.id
    WHERE ugm.user_id = p_user_id
    AND p.permission_code = p_permission_code
    AND ugm.is_active = TRUE
    AND gp.is_granted = TRUE;

    RETURN v_has_permission;
END //
DELIMITER ;
```

### เพิ่มผู้ใช้งานใหม่
```sql
DELIMITER //
CREATE PROCEDURE create_new_user(
    IN p_username VARCHAR(50),
    IN p_email VARCHAR(100),
    IN p_password_hash VARCHAR(255),
    IN p_first_name VARCHAR(50),
    IN p_last_name VARCHAR(50),
    IN p_phone VARCHAR(20),
    IN p_group_id INT,
    IN p_merchant_id INT,
    IN p_parent_user_id INT,
    IN p_created_by INT
)
BEGIN
    DECLARE v_user_id INT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- สร้างผู้ใช้งาน
    INSERT INTO users (username, email, password_hash, first_name, last_name, phone, merchant_id, parent_user_id, created_by)
    VALUES (p_username, p_email, p_password_hash, p_first_name, p_last_name, p_phone, p_merchant_id, p_parent_user_id, p_created_by);

    SET v_user_id = LAST_INSERT_ID();

    -- เพิ่มเข้ากลุ่ม (ถ้าไม่ใช่ user ย่อย)
    IF p_parent_user_id IS NULL THEN
        INSERT INTO user_group_members (user_id, group_id, assigned_by)
        VALUES (v_user_id, p_group_id, p_created_by);
    END IF;

    COMMIT;

    SELECT v_user_id as new_user_id;
END //
DELIMITER ;

-- Procedure: สร้าง User ย่อย (สำหรับเจ้าของร้าน)
DELIMITER //
CREATE PROCEDURE create_user_sub(
    IN p_username VARCHAR(50),
    IN p_email VARCHAR(100),
    IN p_password_hash VARCHAR(255),
    IN p_first_name VARCHAR(50),
    IN p_last_name VARCHAR(50),
    IN p_phone VARCHAR(20),
    IN p_parent_user_id INT
)
BEGIN
    DECLARE v_user_id INT;
    DECLARE v_merchant_id INT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- ดึง merchant_id จาก parent user
    SELECT merchant_id INTO v_merchant_id
    FROM users
    WHERE id = p_parent_user_id;

    -- สร้าง user ย่อย
    INSERT INTO users (username, email, password_hash, first_name, last_name, phone, merchant_id, parent_user_id, created_by)
    VALUES (p_username, p_email, p_password_hash, p_first_name, p_last_name, p_phone, v_merchant_id, p_parent_user_id, p_parent_user_id);

    SET v_user_id = LAST_INSERT_ID();

    COMMIT;

    SELECT v_user_id as new_user_sub_id;
END //
DELIMITER ;
```

---

## 📋 Indexes สำหรับ Performance

```sql
-- Users table indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_verified ON users(is_verified);

-- User Group Members indexes
CREATE INDEX idx_ugm_user ON user_group_members(user_id);
CREATE INDEX idx_ugm_group ON user_group_members(group_id);
CREATE INDEX idx_ugm_active ON user_group_members(is_active);

-- Permissions indexes
CREATE INDEX idx_permissions_code ON permissions(permission_code);
CREATE INDEX idx_permissions_module ON permissions(module);
CREATE INDEX idx_permissions_active ON permissions(is_active);

-- Group Permissions indexes
CREATE INDEX idx_gp_group ON group_permissions(group_id);
CREATE INDEX idx_gp_permission ON group_permissions(permission_id);

-- User Permissions indexes
CREATE INDEX idx_up_user ON user_permissions(user_id);
CREATE INDEX idx_up_permission ON user_permissions(permission_id);
CREATE INDEX idx_up_active ON user_permissions(is_active);
CREATE INDEX idx_up_expires ON user_permissions(expires_at);
```

---

## 🔧 ตัวอย่างการใช้งาน

### ตรวจสอบสิทธิ์ในระบบ
```sql
-- ตรวจสอบว่าเจ้าของร้านกาแฟมีสิทธิ์สร้าง user ย่อยหรือไม่
SELECT check_user_permission(3, 'usersub.create') as can_create_usersub;

-- ดูสิทธิ์ทั้งหมดของเจ้าของร้านกาแฟ
SELECT * FROM view_user_permissions WHERE username = 'coffee_owner';

-- ดูผู้ใช้งานที่มีสิทธิ์จัดการร้านค้า (Backoffice)
SELECT DISTINCT u.username, u.first_name, u.last_name
FROM view_user_permissions vup
JOIN users u ON vup.user_id = u.id
WHERE vup.permission_code = 'merchants.edit'
AND vup.permission_status = 'อนุญาต';

-- ดู User ย่อยทั้งหมดของร้านกาแฟ
SELECT u.username, u.first_name, u.last_name, u.email, u.is_active
FROM users u
WHERE u.parent_user_id = 3 -- เจ้าของร้านกาแฟ
ORDER BY u.created_at;
```

### การจัดการ User ย่อย
```sql
-- เจ้าของร้านสร้าง User ย่อยใหม่
CALL create_user_sub(
    'coffee_delivery',
    '<EMAIL>',
    '$2y$10$example_hash_here',
    'สมรัก',
    'ส่งของ',
    '02-777-7777',
    3 -- parent_user_id (เจ้าของร้านกาแฟ)
);

-- ให้สิทธิ์ User ย่อยใหม่
INSERT INTO user_permissions (user_id, permission_id, permission_type, granted_by) VALUES
(9, 25, 'GRANT', 3), -- merchant.dashboard
(9, 27, 'GRANT', 3); -- merchant.transactions

-- ปิดการใช้งาน User ย่อย
UPDATE users SET is_active = FALSE, updated_by = 3 WHERE id = 6 AND parent_user_id = 3;

-- ดูสิทธิ์ของ User ย่อยทั้งหมดในร้าน
SELECT
    u.username,
    u.first_name,
    u.last_name,
    p.permission_code,
    p.permission_name,
    up.permission_type,
    up.expires_at
FROM users u
LEFT JOIN user_permissions up ON u.id = up.user_id AND up.is_active = TRUE
LEFT JOIN permissions p ON up.permission_id = p.id
WHERE u.parent_user_id = 3 -- เจ้าของร้านกาแฟ
AND u.is_active = TRUE
ORDER BY u.username, p.module;
```

---

## 📝 หมายเหตุการใช้งาน

### 🏢 **ระบบ 3 กลุ่มหลัก:**
1. **SUPER_ADMIN** - ผู้ดูแลระบบสูงสุด (สิทธิ์เต็ม)
2. **ADMIN** - ผู้ดูแลระบบ Backoffice (จัดการร้านค้า, ธุรกรรม, รายงาน)
3. **MERCHANT** - เจ้าของร้าน (จัดการร้านตนเอง + สร้าง User ย่อย)

### 👥 **ระบบ User ย่อย:**
- เจ้าของร้าน (MERCHANT) สามารถสร้าง User ย่อยได้ไม่จำกัด
- User ย่อยจะมี `parent_user_id` ชี้ไปยังเจ้าของร้าน
- User ย่อยไม่อยู่ในกลุ่มใดๆ แต่ได้รับสิทธิ์จากเจ้าของร้านโดยตรง
- เจ้าของร้านสามารถกำหนด/ยกเลิกสิทธิ์ User ย่อยได้

### 🔐 **ระบบสิทธิ์:**
1. **ลำดับความสำคัญ:** DENY > GRANT (เฉพาะบุคคล) > กลุ่ม
2. **การหมดอายุ:** สิทธิ์เฉพาะบุคคลสามารถกำหนดวันหมดอายุได้
3. **2FA:** ระบบรองรับ Google Authenticator สำหรับความปลอดภัยเพิ่มเติม
4. **Audit Trail:** บันทึกผู้สร้างและผู้แก้ไขในทุกตาราง
5. **Soft Delete:** ใช้ is_active แทนการลบข้อมูลจริง

### 🎯 **การแยกระบบ:**
- **Backoffice System:** SUPER_ADMIN, ADMIN ใช้จัดการระบบโดยรวม
- **Merchant System:** MERCHANT และ User ย่อย ใช้จัดการร้านค้าเฉพาะ
- **สิทธิ์แยกชัดเจน:** Backoffice ไม่สามารถเข้าถึงระบบร้านค้า และในทางกลับกัน

ระบบนี้ให้ความยืดหยุ่นในการจัดการสิทธิ์ รองรับการขยายตัว และแยกระบบ Backoffice กับ Merchant อย่างชัดเจน 🚀
```
