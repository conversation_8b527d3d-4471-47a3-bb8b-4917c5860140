# ระบบจัดการผู้ใช้งานแบบง่าย (Simple User Management)
## SugarPay Payment System

---

## 📋 ภาพรวมระบบ

ระบบจัดการผู้ใช้งานของ SugarPay แบบง่าย ประกอบด้วย **2 ตารางเท่านั้น**:
1. **users** - ข้อมูลผู้ใช้งาน + กลุ่ม + สิทธิ์
2. **user_permissions** - สิทธิ์เฉพาะบุคคล (ใช้เฉพาะกรณีพิเศษ)

---

## 🗄️ Database Schema

### 1. ตาราง Users - รวมทุกอย่างในตารางเดียว
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    
    -- กลุ่มผู้ใช้งาน (แทน user_groups table)
    user_group ENUM('SUPER_ADMIN', 'ADMIN', 'MERCHANT') NOT NULL DEFAULT 'MERCHANT',
    
    -- ข้อมูลร้านค้า
    merchant_id INT NULL, -- ถ้าเป็น MERCHANT หรือ user ย่อย
    parent_user_id INT NULL, -- ถ้าเป็น user ย่อย จะระบุ parent (เจ้าของร้าน)
    
    -- สิทธิ์แบบ JSON (แทน permissions tables)
    permissions JSON NULL, -- เก็บสิทธิ์เป็น JSON array เช่น ["merchant.balance", "merchant.transactions"]
    
    -- สถานะ
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    -- 2FA
    google_2fa_secret VARCHAR(255) NULL,
    google_2fa_enabled BOOLEAN DEFAULT FALSE,
    
    -- ข้อมูลการสร้างและแก้ไข
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (parent_user_id) REFERENCES users(id)
);
```

### 2. ตาราง User Permissions - ใช้เฉพาะกรณีพิเศษ
```sql
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    permission_code VARCHAR(50) NOT NULL, -- เช่น "merchant.withdraw"
    permission_type ENUM('GRANT', 'DENY') DEFAULT 'GRANT',
    expires_at TIMESTAMP NULL, -- วันหมดอายุสิทธิ์
    granted_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE KEY unique_user_permission (user_id, permission_code)
);
```

---

## 📊 สิทธิ์เริ่มต้นตามกลุ่ม

### SUPER_ADMIN - สิทธิ์เต็ม
```json
[
  "dashboard.view", "dashboard.stats",
  "users.view", "users.create", "users.edit", "users.delete",
  "merchants.view", "merchants.create", "merchants.edit", "merchants.delete",
  "transactions.view_all", "transactions.approve", "transactions.cancel", "transactions.refund",
  "banks.view", "banks.create", "banks.edit", "banks.delete",
  "reports.view_all", "reports.export", "reports.financial",
  "settings.view", "settings.edit", "settings.backup"
]
```

### ADMIN - สิทธิ์ Backoffice (ไม่รวม backup)
```json
[
  "dashboard.view", "dashboard.stats",
  "users.view", "users.create", "users.edit", "users.delete",
  "merchants.view", "merchants.create", "merchants.edit", "merchants.delete",
  "transactions.view_all", "transactions.approve", "transactions.cancel", "transactions.refund",
  "banks.view", "banks.create", "banks.edit", "banks.delete",
  "reports.view_all", "reports.export", "reports.financial",
  "settings.view", "settings.edit"
]
```

### MERCHANT - สิทธิ์ระบบร้านค้า
```json
[
  "merchant.dashboard", "merchant.balance", "merchant.transactions", 
  "merchant.withdraw", "merchant.reports", "merchant.settings",
  "usersub.view", "usersub.create", "usersub.edit", "usersub.delete", "usersub.permissions"
]
```

---

## 👥 ตัวอย่างการสร้างผู้ใช้งาน

### ผู้ดูแลระบบ
```sql
-- สร้าง Super Admin
INSERT INTO users (id, username, email, password_hash, first_name, last_name, user_group, permissions) VALUES
(1, 'superadmin', '<EMAIL>', '$2y$10$example_hash_here', 'Super', 'Admin', 'SUPER_ADMIN', 
'["dashboard.view", "dashboard.stats", "users.view", "users.create", "users.edit", "users.delete", "merchants.view", "merchants.create", "merchants.edit", "merchants.delete", "transactions.view_all", "transactions.approve", "transactions.cancel", "transactions.refund", "banks.view", "banks.create", "banks.edit", "banks.delete", "reports.view_all", "reports.export", "reports.financial", "settings.view", "settings.edit", "settings.backup"]');

-- สร้าง Admin
INSERT INTO users (id, username, email, password_hash, first_name, last_name, user_group, permissions, created_by) VALUES
(2, 'admin', '<EMAIL>', '$2y$10$example_hash_here', 'System', 'Admin', 'ADMIN',
'["dashboard.view", "dashboard.stats", "users.view", "users.create", "users.edit", "users.delete", "merchants.view", "merchants.create", "merchants.edit", "merchants.delete", "transactions.view_all", "transactions.approve", "transactions.cancel", "transactions.refund", "banks.view", "banks.create", "banks.edit", "banks.delete", "reports.view_all", "reports.export", "reports.financial", "settings.view", "settings.edit"]', 1);
```

### เจ้าของร้านค้า
```sql
-- เจ้าของร้านกาแฟ
INSERT INTO users (id, username, email, password_hash, first_name, last_name, user_group, merchant_id, permissions, created_by) VALUES
(3, 'coffee_owner', '<EMAIL>', '$2y$10$example_hash_here', 'สมศักดิ์', 'เจ้าของร้าน', 'MERCHANT', 1,
'["merchant.dashboard", "merchant.balance", "merchant.transactions", "merchant.withdraw", "merchant.reports", "merchant.settings", "usersub.view", "usersub.create", "usersub.edit", "usersub.delete", "usersub.permissions"]', 1);

-- เจ้าของร้านเสื้อผ้า
INSERT INTO users (id, username, email, password_hash, first_name, last_name, user_group, merchant_id, permissions, created_by) VALUES
(4, 'fashion_owner', '<EMAIL>', '$2y$10$example_hash_here', 'สมหญิง', 'เจ้าของร้าน', 'MERCHANT', 2,
'["merchant.dashboard", "merchant.balance", "merchant.transactions", "merchant.withdraw", "merchant.reports", "merchant.settings", "usersub.view", "usersub.create", "usersub.edit", "usersub.delete", "usersub.permissions"]', 1);
```

### User ย่อย (สร้างโดยเจ้าของร้าน)
```sql
-- พนักงานร้านกาแฟ (ไม่มีสิทธิ์เริ่มต้น)
INSERT INTO users (id, username, email, password_hash, first_name, last_name, user_group, merchant_id, parent_user_id, permissions, created_by) VALUES
(5, 'coffee_staff01', '<EMAIL>', '$2y$10$example_hash_here', 'สมชาย', 'พนักงาน', 'MERCHANT', 1, 3, '[]', 3),
(6, 'coffee_cashier', '<EMAIL>', '$2y$10$example_hash_here', 'สมใจ', 'แคชเชียร์', 'MERCHANT', 1, 3, '[]', 3);

-- พนักงานร้านเสื้อผ้า
INSERT INTO users (id, username, email, password_hash, first_name, last_name, user_group, merchant_id, parent_user_id, permissions, created_by) VALUES
(7, 'fashion_manager', '<EMAIL>', '$2y$10$example_hash_here', 'สมปอง', 'ผู้จัดการ', 'MERCHANT', 2, 4, '[]', 4),
(8, 'fashion_sales', '<EMAIL>', '$2y$10$example_hash_here', 'สมรัก', 'พนักงานขาย', 'MERCHANT', 2, 4, '[]', 4);
```

---

## 🔐 การกำหนดสิทธิ์ให้ User ย่อย

### อัพเดตสิทธิ์ผ่าน JSON
```sql
-- ให้พนักงานร้านกาแฟดูยอดเงินและรายการธุรกรรม
UPDATE users SET permissions = '["merchant.balance", "merchant.transactions"]' WHERE id = 5;

-- ให้แคชเชียร์ดูยอดเงินเท่านั้น
UPDATE users SET permissions = '["merchant.balance"]' WHERE id = 6;

-- ให้ผู้จัดการร้านเสื้อผ้าสิทธิ์เกือบทั้งหมด ยกเว้นการถอนเงิน
UPDATE users SET permissions = '["merchant.dashboard", "merchant.balance", "merchant.transactions", "merchant.reports", "merchant.settings"]' WHERE id = 7;

-- ให้พนักงานขายดูข้อมูลพื้นฐาน
UPDATE users SET permissions = '["merchant.dashboard", "merchant.transactions"]' WHERE id = 8;
```

### ใช้ user_permissions สำหรับกรณีพิเศษ
```sql
-- ให้พนักงานร้านกาแฟสิทธิ์ถอนเงินชั่วคราว 7 วัน
INSERT INTO user_permissions (user_id, permission_code, permission_type, expires_at, granted_by) VALUES
(5, 'merchant.withdraw', 'GRANT', DATE_ADD(NOW(), INTERVAL 7 DAY), 3);

-- ห้ามพนักงานขายดูรายงาน (แม้จะมีสิทธิ์ใน JSON)
INSERT INTO user_permissions (user_id, permission_code, permission_type, granted_by) VALUES
(8, 'merchant.reports', 'DENY', 4);
```

---

## 🔍 Functions สำหรับตรวจสอบสิทธิ์

### ตรวจสอบสิทธิ์ผู้ใช้งาน
```sql
DELIMITER //
CREATE FUNCTION check_user_permission(
    p_user_id INT,
    p_permission_code VARCHAR(50)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_has_permission BOOLEAN DEFAULT FALSE;
    DECLARE v_is_denied BOOLEAN DEFAULT FALSE;
    DECLARE v_user_permissions JSON;
    
    -- ตรวจสอบการปฏิเสธสิทธิ์เฉพาะบุคคล
    SELECT COUNT(*) > 0 INTO v_is_denied
    FROM user_permissions up
    WHERE up.user_id = p_user_id 
    AND up.permission_code = p_permission_code
    AND up.permission_type = 'DENY'
    AND (up.expires_at IS NULL OR up.expires_at > NOW());
    
    IF v_is_denied THEN
        RETURN FALSE;
    END IF;
    
    -- ตรวจสอบสิทธิ์เฉพาะบุคคล (GRANT)
    SELECT COUNT(*) > 0 INTO v_has_permission
    FROM user_permissions up
    WHERE up.user_id = p_user_id 
    AND up.permission_code = p_permission_code
    AND up.permission_type = 'GRANT'
    AND (up.expires_at IS NULL OR up.expires_at > NOW());
    
    IF v_has_permission THEN
        RETURN TRUE;
    END IF;
    
    -- ตรวจสอบสิทธิ์จาก JSON
    SELECT permissions INTO v_user_permissions
    FROM users 
    WHERE id = p_user_id AND is_active = TRUE;
    
    IF v_user_permissions IS NOT NULL AND JSON_CONTAINS(v_user_permissions, JSON_QUOTE(p_permission_code)) THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END //
DELIMITER ;
```

---

## 📋 Indexes สำหรับ Performance

```sql
-- Users table indexes
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_group ON users(user_group);
CREATE INDEX idx_users_merchant ON users(merchant_id);
CREATE INDEX idx_users_parent ON users(parent_user_id);
CREATE INDEX idx_users_active ON users(is_active);

-- User Permissions indexes
CREATE INDEX idx_up_user ON user_permissions(user_id);
CREATE INDEX idx_up_permission ON user_permissions(permission_code);
CREATE INDEX idx_up_expires ON user_permissions(expires_at);
```

---

## 🔧 ตัวอย่างการใช้งาน

### ตรวจสอบสิทธิ์
```sql
-- ตรวจสอบว่าเจ้าของร้านกาแฟมีสิทธิ์สร้าง user ย่อยหรือไม่
SELECT check_user_permission(3, 'usersub.create') as can_create_usersub;

-- ดู User ย่อยทั้งหมดของร้านกาแฟ
SELECT username, first_name, last_name, permissions, is_active
FROM users
WHERE parent_user_id = 3
ORDER BY created_at;

-- ดูสิทธิ์ของผู้ใช้งาน
SELECT 
    u.username,
    u.user_group,
    u.permissions as json_permissions,
    GROUP_CONCAT(CONCAT(up.permission_code, ':', up.permission_type) SEPARATOR ', ') as special_permissions
FROM users u
LEFT JOIN user_permissions up ON u.id = up.user_id 
WHERE u.id = 5
GROUP BY u.id;
```

---

## 📝 ข้อดีของระบบแบบง่าย

### ✅ **ประหยัดทรัพยากร:**
- **2 ตารางเท่านั้น** แทน 6 ตาราง
- ลด JOIN queries ที่ซับซ้อน
- ลด Foreign Key constraints

### ✅ **ง่ายต่อการจัดการ:**
- สิทธิ์เก็บเป็น JSON ใน users table
- ไม่ต้องจัดการ group_permissions
- เพิ่ม/ลบสิทธิ์ง่ายด้วย JSON functions

### ✅ **ยืดหยุ่น:**
- เพิ่มสิทธิ์ใหม่ได้ทันทีโดยไม่ต้องแก้ database
- user_permissions ใช้เฉพาะกรณีพิเศษ (DENY, expires)
- รองรับ User ย่อยได้ไม่จำกัด

### ✅ **Performance ดี:**
- Query เร็วกว่าเพราะ JOIN น้อย
- JSON search ใน MySQL 8.0+ มี performance ดี
- Index ง่ายและมีประสิทธิภาพ

ระบบนี้เหมาะสำหรับ SugarPay ที่ต้องการความเรียบง่ายและประหยัดทรัพยากร! 🚀
